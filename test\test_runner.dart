import 'package:flutter_test/flutter_test.dart';

// Import all test files
import 'widget_test.dart' as widget_tests;
import 'core/utils/validators_test.dart' as validators_tests;
import 'core/utils/date_utils_test.dart' as date_utils_tests;
import 'shared/widgets/loading_widget_test.dart' as loading_widget_tests;
import 'shared/widgets/error_widget_test.dart' as error_widget_tests;
import 'shared/widgets/search_bar_widget_test.dart' as search_bar_widget_tests;

void main() {
  group('All Tests', () {
    group('Widget Tests', widget_tests.main);
    group('Validators Tests', validators_tests.main);
    group('Date Utils Tests', date_utils_tests.main);
    group('Loading Widget Tests', loading_widget_tests.main);
    group('Error Widget Tests', error_widget_tests.main);
    group('Search Bar Widget Tests', search_bar_widget_tests.main);
  });
}
