import 'package:flutter_test/flutter_test.dart';
import 'package:moon_memory_admin/core/utils/validators.dart';

void main() {
  group('Validators', () {
    group('validateEmail', () {
      test('should return null for valid email', () {
        expect(Validators.validateEmail('<EMAIL>'), isNull);
        expect(Validators.validateEmail('<EMAIL>'), isNull);
        expect(Validators.validateEmail('<EMAIL>'), isNull);
      });

      test('should return error for invalid email', () {
        expect(Validators.validateEmail('invalid-email'), isNotNull);
        expect(Validators.validateEmail('test@'), isNotNull);
        expect(Validators.validateEmail('@domain.com'), isNotNull);
        expect(Validators.validateEmail('test.domain.com'), isNotNull);
      });

      test('should return error for empty email', () {
        expect(Validators.validateEmail(''), isNotNull);
        expect(Validators.validateEmail(null), isNotNull);
      });
    });

    group('validatePassword', () {
      test('should return null for valid password', () {
        expect(Validators.validatePassword('12345678'), isNull);
        expect(Validators.validatePassword('password123'), isNull);
        expect(Validators.validatePassword('VeryLongPassword123!'), isNull);
      });

      test('should return error for short password', () {
        expect(Validators.validatePassword('1234567'), isNotNull);
        expect(Validators.validatePassword('short'), isNotNull);
      });

      test('should return error for empty password', () {
        expect(Validators.validatePassword(''), isNotNull);
        expect(Validators.validatePassword(null), isNotNull);
      });
    });

    group('validateNationalId', () {
      test('should return null for valid Saudi national ID', () {
        expect(Validators.validateNationalId('1234567890'), isNull);
        expect(Validators.validateNationalId('2987654321'), isNull);
      });

      test('should return error for invalid length', () {
        expect(Validators.validateNationalId('123456789'), isNotNull);
        expect(Validators.validateNationalId('12345678901'), isNotNull);
      });

      test('should return error for invalid first digit', () {
        expect(Validators.validateNationalId('3234567890'), isNotNull);
        expect(Validators.validateNationalId('0234567890'), isNotNull);
        expect(Validators.validateNationalId('9234567890'), isNotNull);
      });

      test('should return error for non-numeric characters', () {
        expect(Validators.validateNationalId('123456789a'), isNotNull);
        expect(Validators.validateNationalId('12345-6789'), isNotNull);
      });

      test('should return error for empty national ID', () {
        expect(Validators.validateNationalId(''), isNotNull);
        expect(Validators.validateNationalId(null), isNotNull);
      });
    });

    group('validateUsername', () {
      test('should return null for valid username', () {
        expect(Validators.validateUsername('user123'), isNull);
        expect(Validators.validateUsername('test_user'), isNull);
        expect(Validators.validateUsername('USERNAME'), isNull);
      });

      test('should return error for invalid characters', () {
        expect(Validators.validateUsername('user-name'), isNotNull);
        expect(Validators.validateUsername('user name'), isNotNull);
        expect(Validators.validateUsername('user@name'), isNotNull);
      });

      test('should return error for too long username', () {
        final longUsername = 'a' * 51;
        expect(Validators.validateUsername(longUsername), isNotNull);
      });

      test('should return error for empty username', () {
        expect(Validators.validateUsername(''), isNotNull);
        expect(Validators.validateUsername(null), isNotNull);
      });
    });

    group('validateFullName', () {
      test('should return null for valid full name', () {
        expect(Validators.validateFullName('أحمد محمد'), isNull);
        expect(Validators.validateFullName('Ahmed Mohammed'), isNull);
        expect(Validators.validateFullName('نورا عبدالله الأحمد'), isNull);
      });

      test('should return error for too long name', () {
        final longName = 'أ' * 101;
        expect(Validators.validateFullName(longName), isNotNull);
      });

      test('should return error for empty name', () {
        expect(Validators.validateFullName(''), isNotNull);
        expect(Validators.validateFullName('   '), isNotNull);
        expect(Validators.validateFullName(null), isNotNull);
      });
    });

    group('validatePhoneNumber', () {
      test('should return null for valid Saudi phone numbers', () {
        expect(Validators.validatePhoneNumber('0501234567'), isNull);
        expect(Validators.validatePhoneNumber('966501234567'), isNull);
        expect(Validators.validatePhoneNumber('0112345678'), isNull);
        expect(Validators.validatePhoneNumber('966112345678'), isNull);
      });

      test('should return null for empty phone (optional field)', () {
        expect(Validators.validatePhoneNumber(''), isNull);
        expect(Validators.validatePhoneNumber(null), isNull);
      });

      test('should return error for invalid phone numbers', () {
        expect(Validators.validatePhoneNumber('123456789'), isNotNull);
        expect(Validators.validatePhoneNumber('0401234567'), isNotNull);
        expect(Validators.validatePhoneNumber('966401234567'), isNotNull);
        expect(Validators.validatePhoneNumber('1234567890'), isNotNull);
        expect(Validators.validatePhoneNumber('abcdefghij'), isNotNull);
      });
    });

    group('validateRequired', () {
      test('should return null for non-empty value', () {
        expect(Validators.validateRequired('test', 'Field'), isNull);
        expect(Validators.validateRequired('   test   ', 'Field'), isNull);
      });

      test('should return error for empty value', () {
        expect(Validators.validateRequired('', 'Field'), isNotNull);
        expect(Validators.validateRequired('   ', 'Field'), isNotNull);
        expect(Validators.validateRequired(null, 'Field'), isNotNull);
      });
    });
  });
}
