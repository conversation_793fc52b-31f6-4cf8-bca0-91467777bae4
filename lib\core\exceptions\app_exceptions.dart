/// Base exception class for the application
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const AppException(this.message, {this.code, this.originalError});

  @override
  String toString() => message;
}

/// Network related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code, super.originalError});
}

/// Authentication related exceptions
class AuthException extends AppException {
  const AuthException(super.message, {super.code, super.originalError});
}

/// Validation related exceptions
class ValidationException extends AppException {
  const ValidationException(super.message, {super.code, super.originalError});
}

/// Server related exceptions
class ServerException extends AppException {
  const ServerException(super.message, {super.code, super.originalError});
}

/// Cache related exceptions
class CacheException extends AppException {
  const CacheException(super.message, {super.code, super.originalError});
}

/// Permission related exceptions
class PermissionException extends AppException {
  const PermissionException(super.message, {super.code, super.originalError});
}

/// Data not found exceptions
class NotFoundException extends AppException {
  const NotFoundException(super.message, {super.code, super.originalError});
}

/// Timeout exceptions
class TimeoutException extends AppException {
  const TimeoutException(super.message, {super.code, super.originalError});
}

/// Exception handler utility
class ExceptionHandler {
  /// Convert various exceptions to user-friendly messages
  static String getErrorMessage(dynamic error) {
    if (error is AppException) {
      return error.message;
    }
    
    if (error is Exception) {
      final errorString = error.toString();
      
      // Handle common Supabase errors
      if (errorString.contains('JWT')) {
        return 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى';
      }
      
      if (errorString.contains('network') || errorString.contains('connection')) {
        return 'خطأ في الاتصال بالشبكة';
      }
      
      if (errorString.contains('timeout')) {
        return 'انتهت مهلة الاتصال';
      }
      
      if (errorString.contains('permission') || errorString.contains('unauthorized')) {
        return 'ليس لديك صلاحية للوصول';
      }
      
      if (errorString.contains('not found')) {
        return 'البيانات المطلوبة غير موجودة';
      }
    }
    
    return 'حدث خطأ غير متوقع';
  }

  /// Log error with context
  static void logError(dynamic error, {String? context, StackTrace? stackTrace}) {
    // In a real app, you might want to send this to a crash reporting service
    print('Error in $context: $error');
    if (stackTrace != null) {
      print('Stack trace: $stackTrace');
    }
  }
}
