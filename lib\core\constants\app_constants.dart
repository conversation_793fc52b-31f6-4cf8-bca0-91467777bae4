/// Application constants
class AppConstants {
  // Database table names
  static const String userTable = 'users';
  static const String deviceTable = 'devices';
  static const String loginAttemptsTable = 'login_attempts';
  static const String photosTable = 'photos';
  static const String adminsTable = 'admins';
  
  // Storage bucket names
  static const String photosBucket = 'photos';
  
  // Account status constants
  static const String statusActive = 'active';
  static const String statusSuspended = 'suspended';
  static const String statusBanned = 'banned';
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Retry configuration
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 1);
  
  // Image transformations
  static const String thumbnailTransform = 'thumbnail';
  static const String mediumTransform = 'medium';
  
  // Date formats
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';
  static const String dateFormat = 'yyyy-MM-dd';
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxUsernameLength = 50;
  static const int maxFullNameLength = 100;
}

/// Error messages
class ErrorMessages {
  static const String networkError = 'خطأ في الاتصال بالشبكة';
  static const String unknownError = 'حدث خطأ غير متوقع';
  static const String invalidCredentials = 'بيانات الدخول غير صحيحة';
  static const String userNotFound = 'المستخدم غير موجود';
  static const String accessDenied = 'ليس لديك صلاحية للوصول';
  static const String dataLoadError = 'فشل في تحميل البيانات';
  static const String saveError = 'فشل في حفظ البيانات';
  static const String deleteError = 'فشل في حذف البيانات';
  static const String validationError = 'بيانات غير صحيحة';
}

/// Success messages
class SuccessMessages {
  static const String userCreated = 'تم إنشاء المستخدم بنجاح';
  static const String userUpdated = 'تم تحديث المستخدم بنجاح';
  static const String userDeleted = 'تم حذف المستخدم بنجاح';
  static const String userSuspended = 'تم تعليق المستخدم بنجاح';
  static const String userActivated = 'تم تفعيل المستخدم بنجاح';
  static const String photoDeleted = 'تم حذف الصورة بنجاح';
  static const String dataLoaded = 'تم تحميل البيانات بنجاح';
  static const String dataSaved = 'تم حفظ البيانات بنجاح';
}
