import 'package:flutter_dotenv/flutter_dotenv.dart';

class SupabaseConfig {
  /// Project URL from environment variables
  static String get supabaseUrl {
    final url = dotenv.env['SUPABASE_URL'];
    if (url == null || url.isEmpty) {
      throw Exception('SUPABASE_URL not found in environment variables');
    }
    return url;
  }

  /// Service role key from environment variables
  static String get supabaseServiceKey {
    final key = dotenv.env['SUPABASE_SERVICE_KEY'];
    if (key == null || key.isEmpty) {
      throw Exception('SUPABASE_SERVICE_KEY not found in environment variables');
    }
    return key;
  }

  /// Anonymous key from environment variables
  static String get supabaseAnonKey {
    final key = dotenv.env['SUPABASE_ANON_KEY'];
    if (key == null || key.isEmpty) {
      throw Exception('SUPABASE_ANON_KEY not found in environment variables');
    }
    return key;
  }
}
