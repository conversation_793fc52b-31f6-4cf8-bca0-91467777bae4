{"version": "2.0.0", "tasks": [{"label": "Flutter: Get Packages", "type": "shell", "command": "flutter", "args": ["pub", "get"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Clean", "type": "shell", "command": "flutter", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Run Tests", "type": "shell", "command": "flutter", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Run Tests with Coverage", "type": "shell", "command": "flutter", "args": ["test", "--coverage"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Ana<PERSON>ze", "type": "shell", "command": "flutter", "args": ["analyze"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Format Code", "type": "shell", "command": "dart", "args": ["format", "lib/", "test/"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Build Web", "type": "shell", "command": "flutter", "args": ["build", "web", "--release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Build Windows", "type": "shell", "command": "flutter", "args": ["build", "windows", "--release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}