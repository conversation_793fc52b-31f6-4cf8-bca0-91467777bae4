import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';

/// Service for managing photos in the admin panel
class PhotosService {
  final SupabaseClient _client;
  final logger = Logger();
  static const String _photosBucket = 'photos';
  static const int _pageSize = 20;

  PhotosService(this._client);

  /// Fetches photos for a specific user with pagination
  Future<PhotosResult> getUserPhotos(String userId, {int page = 1}) async {
    try {
      // حساب التخطي
      final skip = (page - 1) * _pageSize;

      // جلب الصور مع المعلومات الأساسية فقط
      final response = await _client
          .from('photos')
          .select('id, storage_path, date_time')
          .eq('user_id', userId)
          .order('date_time', ascending: false)
          .range(skip, skip + _pageSize - 1);

      final photos = List<Map<String, dynamic>>.from(response);
      final validPhotos = <Map<String, dynamic>>[];

      // معالجة الصور بشكل متوازي
      await Future.wait(
        photos.map((photo) async {
          try {
            final storagePath = photo['storage_path'] as String?;
            if (storagePath == null) return;

            final imageUrl = await getPhotoUrl(storagePath);
            if (imageUrl == null) return;

            final thumbnailUrl = await getPhotoUrl(storagePath, transform: 'thumbnail');
            validPhotos.add({
              ...photo,
              'image_url': imageUrl,
              'thumbnail_url': thumbnailUrl ?? imageUrl,
            });
          } catch (e) {
            logger.e('Error processing photo: $e');
          }
        }),
      );

      // جلب العدد الإجمالي بشكل منفصل
      final countResponse = await _client
          .from('photos')
          .select('id')
          .eq('user_id', userId)
          .count();

      return PhotosResult(
        photos: validPhotos,
        totalCount: countResponse.count,
        currentPage: page,
        hasMore: skip + validPhotos.length < countResponse.count,
      );
    } catch (e) {
      logger.e('Error loading photos: $e');
      throw 'فشل في تحميل الصور. الرجاء المحاولة مرة أخرى';
    }
  }

  /// Get photo URL with optional transformation
  Future<String?> getPhotoUrl(String storagePath, {String? transform}) async {
    try {
      final signedUrl = await _client.storage
          .from(_photosBucket)
          .createSignedUrl(storagePath, 3600);
      
      if (transform == null) return signedUrl;
      
      final uri = Uri.parse(signedUrl);
      final params = Map<String, String>.from(uri.queryParameters);
      params['transform'] = transform;
      
      return uri.replace(queryParameters: params).toString();
    } catch (e) {
      logger.e('Error generating URL: $e');
      return null;
    }
  }

  /// Delete a photo completely
  Future<void> deletePhoto(String storagePath, String photoId) async {
    try {
      logger.i('Starting to delete photo...');
      logger.i('Storage path: $storagePath');
      logger.i('Photo ID: $photoId');
      
      logger.i('Removing from storage...');
      await _client.storage.from(_photosBucket).remove([storagePath]);
      
      logger.i('Deleting from database...');
      final response = await _client
          .from('photos')
          .delete()
          .eq('id', photoId)
          .select();
      logger.i('Delete response: $response');
      
      logger.i('Photo deleted successfully');
    } catch (e) {
      logger.e('Error deleting photo: $e');
      throw 'فشل في حذف الصورة. الرجاء المحاولة مرة أخرى';
    }
  }
}

/// Result class for paginated photos
class PhotosResult {
  final List<Map<String, dynamic>> photos;
  final int totalCount;
  final int currentPage;
  final bool hasMore;

  PhotosResult({
    required this.photos,
    required this.totalCount,
    required this.currentPage,
    required this.hasMore,
  });
}

final photosServiceProvider = Provider<PhotosService>((ref) {
  return PhotosService(Supabase.instance.client);
});
