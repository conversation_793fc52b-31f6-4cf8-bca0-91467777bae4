import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:moon_memory_admin/shared/widgets/error_widget.dart';

void main() {
  group('ErrorDisplayWidget', () {
    testWidgets('should display default error message when no error provided', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ErrorDisplayWidget(),
          ),
        ),
      );

      expect(find.byType(Icon), findsOneWidget);
      expect(find.text('حدث خطأ غير متوقع'), findsOneWidget);
    });

    testWidgets('should display custom error message when provided', (tester) async {
      const customError = 'Custom error message';
      
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ErrorDisplayWidget(error: customError),
          ),
        ),
      );

      expect(find.text(customError), findsOneWidget);
      expect(find.byType(Icon), findsOneWidget);
    });

    testWidgets('should display retry button when onRetry provided', (tester) async {
      bool retryPressed = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ErrorDisplayWidget(
              onRetry: () {
                retryPressed = true;
              },
            ),
          ),
        ),
      );

      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.text('إعادة المحاولة'), findsOneWidget);

      await tester.tap(find.byType(ElevatedButton));
      expect(retryPressed, isTrue);
    });

    testWidgets('should not display retry button when onRetry not provided', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ErrorDisplayWidget(),
          ),
        ),
      );

      expect(find.byType(ElevatedButton), findsNothing);
      expect(find.text('إعادة المحاولة'), findsNothing);
    });

    testWidgets('should use custom icon when provided', (tester) async {
      const customIcon = Icons.warning;
      
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ErrorDisplayWidget(icon: customIcon),
          ),
        ),
      );

      final iconWidget = tester.widget<Icon>(find.byType(Icon));
      expect(iconWidget.icon, equals(customIcon));
    });
  });

  group('EmptyStateWidget', () {
    testWidgets('should display message and default icon', (tester) async {
      const message = 'No data available';
      
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EmptyStateWidget(message: message),
          ),
        ),
      );

      expect(find.text(message), findsOneWidget);
      expect(find.byType(Icon), findsOneWidget);
      
      final iconWidget = tester.widget<Icon>(find.byType(Icon));
      expect(iconWidget.icon, equals(Icons.inbox_outlined));
    });

    testWidgets('should display custom icon when provided', (tester) async {
      const message = 'No data available';
      const customIcon = Icons.folder_open;
      
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EmptyStateWidget(
              message: message,
              icon: customIcon,
            ),
          ),
        ),
      );

      final iconWidget = tester.widget<Icon>(find.byType(Icon));
      expect(iconWidget.icon, equals(customIcon));
    });

    testWidgets('should display action widget when provided', (tester) async {
      const message = 'No data available';
      const actionText = 'Add Item';
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EmptyStateWidget(
              message: message,
              action: ElevatedButton(
                onPressed: () {},
                child: const Text(actionText),
              ),
            ),
          ),
        ),
      );

      expect(find.text(message), findsOneWidget);
      expect(find.text(actionText), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should not display action when not provided', (tester) async {
      const message = 'No data available';
      
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EmptyStateWidget(message: message),
          ),
        ),
      );

      expect(find.text(message), findsOneWidget);
      expect(find.byType(ElevatedButton), findsNothing);
    });
  });
}
