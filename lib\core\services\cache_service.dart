import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../exceptions/app_exceptions.dart';

/// Simple cache service using SharedPreferences
class CacheService {
  static CacheService? _instance;
  static SharedPreferences? _prefs;

  CacheService._();

  static Future<CacheService> getInstance() async {
    _instance ??= CacheService._();
    _prefs ??= await SharedPreferences.getInstance();
    return _instance!;
  }

  /// Cache expiry duration (default: 1 hour)
  static const Duration defaultCacheDuration = Duration(hours: 1);

  /// Cache a string value
  Future<void> cacheString(String key, String value, {Duration? duration}) async {
    try {
      final expiryTime = DateTime.now().add(duration ?? defaultCacheDuration);
      final cacheData = {
        'value': value,
        'expiry': expiryTime.millisecondsSinceEpoch,
      };
      await _prefs!.setString(key, jsonEncode(cacheData));
    } catch (e) {
      throw CacheException('Failed to cache string: $e', originalError: e);
    }
  }

  /// Get cached string value
  Future<String?> getCachedString(String key) async {
    try {
      final cachedData = _prefs!.getString(key);
      if (cachedData == null) return null;

      final data = jsonDecode(cachedData) as Map<String, dynamic>;
      final expiryTime = DateTime.fromMillisecondsSinceEpoch(data['expiry']);
      
      if (DateTime.now().isAfter(expiryTime)) {
        await _prefs!.remove(key);
        return null;
      }

      return data['value'] as String;
    } catch (e) {
      throw CacheException('Failed to get cached string: $e', originalError: e);
    }
  }

  /// Cache a JSON object
  Future<void> cacheJson(String key, Map<String, dynamic> value, {Duration? duration}) async {
    try {
      final jsonString = jsonEncode(value);
      await cacheString(key, jsonString, duration: duration);
    } catch (e) {
      throw CacheException('Failed to cache JSON: $e', originalError: e);
    }
  }

  /// Get cached JSON object
  Future<Map<String, dynamic>?> getCachedJson(String key) async {
    try {
      final cachedString = await getCachedString(key);
      if (cachedString == null) return null;
      
      return jsonDecode(cachedString) as Map<String, dynamic>;
    } catch (e) {
      throw CacheException('Failed to get cached JSON: $e', originalError: e);
    }
  }

  /// Cache a list of JSON objects
  Future<void> cacheJsonList(String key, List<Map<String, dynamic>> value, {Duration? duration}) async {
    try {
      final jsonString = jsonEncode(value);
      await cacheString(key, jsonString, duration: duration);
    } catch (e) {
      throw CacheException('Failed to cache JSON list: $e', originalError: e);
    }
  }

  /// Get cached list of JSON objects
  Future<List<Map<String, dynamic>>?> getCachedJsonList(String key) async {
    try {
      final cachedString = await getCachedString(key);
      if (cachedString == null) return null;
      
      final decoded = jsonDecode(cachedString) as List;
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      throw CacheException('Failed to get cached JSON list: $e', originalError: e);
    }
  }

  /// Remove cached item
  Future<void> remove(String key) async {
    try {
      await _prefs!.remove(key);
    } catch (e) {
      throw CacheException('Failed to remove cached item: $e', originalError: e);
    }
  }

  /// Clear all cache
  Future<void> clearAll() async {
    try {
      await _prefs!.clear();
    } catch (e) {
      throw CacheException('Failed to clear cache: $e', originalError: e);
    }
  }

  /// Check if key exists and is not expired
  Future<bool> exists(String key) async {
    try {
      final cachedData = _prefs!.getString(key);
      if (cachedData == null) return false;

      final data = jsonDecode(cachedData) as Map<String, dynamic>;
      final expiryTime = DateTime.fromMillisecondsSinceEpoch(data['expiry']);
      
      if (DateTime.now().isAfter(expiryTime)) {
        await _prefs!.remove(key);
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get cache size (approximate)
  Future<int> getCacheSize() async {
    try {
      final keys = _prefs!.getKeys();
      int totalSize = 0;
      
      for (final key in keys) {
        final value = _prefs!.getString(key);
        if (value != null) {
          totalSize += value.length;
        }
      }
      
      return totalSize;
    } catch (e) {
      return 0;
    }
  }
}
