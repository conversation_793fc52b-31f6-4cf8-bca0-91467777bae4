import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'core/config/supabase_config.dart';
import 'core/theme/app_theme.dart';
import 'core/utils/logger.dart';
import 'features/users/presentation/users_list_screen.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    logger.i('Initializing Supabase...');
    
    // تهيئة Supabase مع service role key
    await Supabase.initialize(
      url: SupabaseConfig.supabaseUrl,
      anonKey: SupabaseConfig.supabaseServiceKey,
      headers: {
        'apikey': SupabaseConfig.supabaseServiceKey,
      },
    );
    logger.i('Supabase initialized successfully');

    // اختبار الاتصال
    logger.i('Testing Supabase connection...');
    final client = SupabaseClient(
      SupabaseConfig.supabaseUrl,
      SupabaseConfig.supabaseServiceKey,
      headers: {'apikey': SupabaseConfig.supabaseServiceKey},
    );
    final response = await client.rpc('get_service_role').single();
    logger.i('Connection test successful. Response: $response');
    
  } catch (e) {
    logger.e('Error initializing Supabase', error: e);
  }

  runApp(
    const ProviderScope(
      child: AdminApp(),
    ),
  );
}

class AdminApp extends StatelessWidget {
  const AdminApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Moon Memory Admin',
      theme: AppTheme.lightTheme,
      home: const UsersListScreen(),
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: child!,
        );
      },
    );
  }
}
