import 'package:flutter_test/flutter_test.dart';
import 'package:moon_memory_admin/core/utils/date_utils.dart' as app_date_utils;

void main() {
  group('DateUtils', () {
    final testDate = DateTime(2024, 1, 15, 14, 30, 0);
    
    group('formatDateTime', () {
      test('should format DateTime correctly', () {
        final result = app_date_utils.DateUtils.formatDateTime(testDate);
        expect(result, equals('2024-01-15 14:30'));
      });

      test('should return dash for null DateTime', () {
        final result = app_date_utils.DateUtils.formatDateTime(null);
        expect(result, equals('-'));
      });
    });

    group('formatDate', () {
      test('should format date correctly', () {
        final result = app_date_utils.DateUtils.formatDate(testDate);
        expect(result, equals('2024-01-15'));
      });

      test('should return dash for null DateTime', () {
        final result = app_date_utils.DateUtils.formatDate(null);
        expect(result, equals('-'));
      });
    });

    group('getRelativeTime', () {
      test('should return "الآن" for recent time', () {
        final now = DateTime.now();
        final recent = now.subtract(const Duration(seconds: 30));
        final result = app_date_utils.DateUtils.getRelativeTime(recent);
        expect(result, equals('الآن'));
      });

      test('should return minutes for time within hour', () {
        final now = DateTime.now();
        final fiveMinutesAgo = now.subtract(const Duration(minutes: 5));
        final result = app_date_utils.DateUtils.getRelativeTime(fiveMinutesAgo);
        expect(result, contains('دقائق'));
      });

      test('should return hours for time within day', () {
        final now = DateTime.now();
        final twoHoursAgo = now.subtract(const Duration(hours: 2));
        final result = app_date_utils.DateUtils.getRelativeTime(twoHoursAgo);
        expect(result, contains('ساعات'));
      });

      test('should return days for older time', () {
        final now = DateTime.now();
        final threeDaysAgo = now.subtract(const Duration(days: 3));
        final result = app_date_utils.DateUtils.getRelativeTime(threeDaysAgo);
        expect(result, contains('أيام'));
      });

      test('should return dash for null DateTime', () {
        final result = app_date_utils.DateUtils.getRelativeTime(null);
        expect(result, equals('-'));
      });
    });

    group('isToday', () {
      test('should return true for today\'s date', () {
        final now = DateTime.now();
        final result = app_date_utils.DateUtils.isToday(now);
        expect(result, isTrue);
      });

      test('should return false for yesterday\'s date', () {
        final yesterday = DateTime.now().subtract(const Duration(days: 1));
        final result = app_date_utils.DateUtils.isToday(yesterday);
        expect(result, isFalse);
      });

      test('should return false for null DateTime', () {
        final result = app_date_utils.DateUtils.isToday(null);
        expect(result, isFalse);
      });
    });

    group('isYesterday', () {
      test('should return true for yesterday\'s date', () {
        final yesterday = DateTime.now().subtract(const Duration(days: 1));
        final result = app_date_utils.DateUtils.isYesterday(yesterday);
        expect(result, isTrue);
      });

      test('should return false for today\'s date', () {
        final now = DateTime.now();
        final result = app_date_utils.DateUtils.isYesterday(now);
        expect(result, isFalse);
      });

      test('should return false for null DateTime', () {
        final result = app_date_utils.DateUtils.isYesterday(null);
        expect(result, isFalse);
      });
    });

    group('parseDateTime', () {
      test('should parse valid ISO string', () {
        const dateString = '2024-01-15T14:30:00.000Z';
        final result = app_date_utils.DateUtils.parseDateTime(dateString);
        expect(result, isNotNull);
        expect(result!.year, equals(2024));
        expect(result.month, equals(1));
        expect(result.day, equals(15));
      });

      test('should return null for invalid string', () {
        const invalidString = 'invalid-date';
        final result = app_date_utils.DateUtils.parseDateTime(invalidString);
        expect(result, isNull);
      });

      test('should return null for null input', () {
        final result = app_date_utils.DateUtils.parseDateTime(null);
        expect(result, isNull);
      });

      test('should return null for empty string', () {
        final result = app_date_utils.DateUtils.parseDateTime('');
        expect(result, isNull);
      });
    });

    group('getAge', () {
      test('should calculate age correctly', () {
        final birthDate = DateTime(1990, 5, 15);
        final age = app_date_utils.DateUtils.getAge(birthDate);
        expect(age, isNotNull);
        expect(age! >= 33, isTrue); // Assuming current year is 2024 or later
      });

      test('should handle birthday not yet occurred this year', () {
        final now = DateTime.now();
        final birthDate = DateTime(now.year - 25, now.month + 1, now.day);
        final age = app_date_utils.DateUtils.getAge(birthDate);
        expect(age, equals(24)); // Should be one year less
      });

      test('should return null for null birth date', () {
        final age = app_date_utils.DateUtils.getAge(null);
        expect(age, isNull);
      });
    });

    group('getSmartDateFormat', () {
      test('should return "اليوم" for today\'s date', () {
        final now = DateTime.now();
        final result = app_date_utils.DateUtils.getSmartDateFormat(now);
        expect(result, contains('اليوم'));
      });

      test('should return "أمس" for yesterday\'s date', () {
        final yesterday = DateTime.now().subtract(const Duration(days: 1));
        final result = app_date_utils.DateUtils.getSmartDateFormat(yesterday);
        expect(result, contains('أمس'));
      });

      test('should return formatted date for older dates', () {
        final oldDate = DateTime.now().subtract(const Duration(days: 5));
        final result = app_date_utils.DateUtils.getSmartDateFormat(oldDate);
        expect(result, isNot(contains('اليوم')));
        expect(result, isNot(contains('أمس')));
      });

      test('should return dash for null DateTime', () {
        final result = app_date_utils.DateUtils.getSmartDateFormat(null);
        expect(result, equals('-'));
      });
    });
  });
}
