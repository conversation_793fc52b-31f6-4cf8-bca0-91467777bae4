{"dart.flutterSdkPath": null, "dart.lineLength": 100, "dart.insertArgumentPlaceholders": false, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.closingLabels": true, "dart.showTodos": true, "dart.runPubGetOnPubspecChanges": true, "dart.warnWhenEditingFilesOutsideWorkspace": true, "dart.analysisExcludedFolders": ["build"], "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/build": true, "**/.dart_tool": true, "**/.packages": true, "**/.pub": true}, "files.watcherExclude": {"**/build/**": true, "**/.dart_tool/**": true, "**/.pub/**": true}, "editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [100], "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}, "files.associations": {"*.dart": "dart"}, "emmet.includeLanguages": {"dart": "html"}}