import '../constants/app_constants.dart';

/// Validation utilities
class Validators {
  /// Validate email format
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }

    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'صيغة البريد الإلكتروني غير صحيحة';
    }

    return null;
  }

  /// Validate password
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }

    if (value.length < AppConstants.minPasswordLength) {
      return 'كلمة المرور يجب أن تكون ${AppConstants.minPasswordLength} أحرف على الأقل';
    }

    return null;
  }

  /// Validate required field
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName مطلوب';
    }
    return null;
  }

  /// Validate username
  static String? validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return 'اسم المستخدم مطلوب';
    }

    if (value.length > AppConstants.maxUsernameLength) {
      return 'اسم المستخدم لا يجب أن يتجاوز ${AppConstants.maxUsernameLength} حرف';
    }

    final usernameRegex = RegExp(r'^[a-zA-Z0-9_]+$');
    if (!usernameRegex.hasMatch(value)) {
      return 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام و _ فقط';
    }

    return null;
  }

  /// Validate full name
  static String? validateFullName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'الاسم الكامل مطلوب';
    }

    if (value.trim().length > AppConstants.maxFullNameLength) {
      return 'الاسم الكامل لا يجب أن يتجاوز ${AppConstants.maxFullNameLength} حرف';
    }

    return null;
  }

  /// Validate national ID (Saudi format)
  static String? validateNationalId(String? value) {
    if (value == null || value.isEmpty) {
      return 'رقم الهوية مطلوب';
    }

    // Remove any spaces or dashes
    final cleanValue = value.replaceAll(RegExp(r'[\s-]'), '');

    // Check if it's exactly 10 digits
    if (cleanValue.length != 10) {
      return 'رقم الهوية يجب أن يكون 10 أرقام';
    }

    // Check if all characters are digits
    if (!RegExp(r'^\d+$').hasMatch(cleanValue)) {
      return 'رقم الهوية يجب أن يحتوي على أرقام فقط';
    }

    // Basic validation for Saudi national ID
    final firstDigit = int.parse(cleanValue[0]);
    if (firstDigit != 1 && firstDigit != 2) {
      return 'رقم الهوية غير صحيح';
    }

    return null;
  }

  /// Validate phone number (Saudi format)
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Phone is optional
    }

    // Remove any spaces, dashes, or plus signs
    final cleanValue = value.replaceAll(RegExp(r'[\s\-\+]'), '');

    // Saudi phone number patterns - more flexible
    final saudiMobileRegex = RegExp(r'^(966|0)?5[0-9]{8}$');
    final saudiLandlineRegex = RegExp(r'^(966|0)?1[1-9][0-9]{7}$');

    if (!saudiMobileRegex.hasMatch(cleanValue) && !saudiLandlineRegex.hasMatch(cleanValue)) {
      return 'رقم الهاتف غير صحيح';
    }

    return null;
  }
}
