# Moon Memory Admin Panel

لوحة تحكم إدارية لتطبيق Moon Memory مبنية بـ Flutter. تتيح هذه اللوحة إدارة المستخدمين وأجهزتهم وصورهم ومراقبة نشاطاتهم.

## 🚀 المميزات

### إدارة المستخدمين
- ✅ عرض قائمة شاملة بجميع المستخدمين
- ✅ البحث والتصفية المتقدمة
- ✅ إضافة مستخدمين جدد
- ✅ تعديل بيانات المستخدمين
- ✅ تفعيل/تعطيل الحسابات
- ✅ حذف المستخدمين

### إدارة الأجهزة
- ✅ عرض أجهزة كل مستخدم
- ✅ تتبع آخر تسجيل دخول
- ✅ إدارة حالة الأجهزة (نشط/غير نشط)
- ✅ حذف الأجهزة

### إدارة الصور
- ✅ عرض صور المستخدمين
- ✅ معاينة الصور بحجم كامل
- ✅ حذف الصور
- ✅ نظام pagination للأداء الأمثل

### مراقبة النشاط
- ✅ تتبع محاولات تسجيل الدخول
- ✅ عرض تفاصيل النشاط
- ✅ إحصائيات شاملة

## 🛠️ التقنيات المستخدمة

- **Flutter 3.29.3** - إطار العمل الأساسي
- **Dart 3.7.2** - لغة البرمجة
- **Supabase** - قاعدة البيانات والخدمات الخلفية
- **Riverpod** - إدارة الحالة
- **Google Fonts** - الخطوط (Cairo للعربية)
- **Material Design 3** - نظام التصميم

## 📁 بنية المشروع

```
lib/
├── core/                    # الملفات الأساسية
│   ├── config/             # إعدادات التطبيق
│   ├── constants/          # الثوابت
│   ├── exceptions/         # معالجة الأخطاء
│   ├── services/           # الخدمات
│   ├── theme/              # التصميم والألوان
│   └── utils/              # الأدوات المساعدة
├── features/               # ميزات التطبيق
│   └── users/             # إدارة المستخدمين
│       └── presentation/   # واجهات المستخدم
├── shared/                 # المكونات المشتركة
│   └── widgets/           # الـ widgets المشتركة
└── main.dart              # نقطة البداية
```

## ⚙️ التثبيت والإعداد

### المتطلبات الأساسية
- Flutter SDK 3.29.3 أو أحدث
- Dart SDK 3.7.2 أو أحدث
- حساب Supabase نشط

### خطوات التثبيت

1. **استنساخ المشروع**
   ```bash
   git clone <repository-url>
   cd moon_memory_admin
   ```

2. **تثبيت التبعيات**
   ```bash
   flutter pub get
   ```

3. **إعداد متغيرات البيئة**
   ```bash
   cp .env.example .env
   ```

   ثم قم بتعديل ملف `.env` وإضافة بيانات Supabase الخاصة بك:
   ```env
   SUPABASE_URL=your_supabase_project_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_KEY=your_supabase_service_role_key
   ```

4. **إعداد قاعدة البيانات**
   - قم بتشغيل ملف `supabase/init.sql` في قاعدة بيانات Supabase
   - قم بتشغيل ملف `supabase/rls_policies.sql` لإعداد سياسات الأمان

5. **تشغيل التطبيق**
   ```bash
   flutter run
   ```

## 🧪 الاختبارات

### تشغيل جميع الاختبارات
```bash
flutter test
```

### تشغيل اختبارات محددة
```bash
# اختبارات الـ widgets
flutter test test/shared/widgets/

# اختبارات الأدوات المساعدة
flutter test test/core/utils/

# اختبار محدد
flutter test test/core/utils/validators_test.dart
```

### تشغيل الاختبارات مع تقرير التغطية
```bash
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

## 🔒 الأمان

### متغيرات البيئة
- **لا تقم أبداً** بإضافة ملف `.env` إلى Git
- استخدم `.env.example` كمرجع للمتغيرات المطلوبة
- تأكد من أن مفاتيح Supabase محمية بشكل صحيح

### سياسات قاعدة البيانات
- Row Level Security (RLS) مفعل على جميع الجداول
- استخدام Service Role Key للوصول الإداري الكامل
- سياسات أمان محددة لكل جدول

## 📱 الاستخدام

### تسجيل الدخول
التطبيق يستخدم Service Role Key للوصول المباشر إلى قاعدة البيانات دون الحاجة لتسجيل دخول.

### إدارة المستخدمين
1. افتح التطبيق للوصول إلى قائمة المستخدمين
2. استخدم شريط البحث للعثور على مستخدمين محددين
3. انقر على مستخدم لعرض تفاصيله
4. استخدم الأزرار لتعديل حالة المستخدم أو حذفه

### إضافة مستخدم جديد
1. انقر على زر "إضافة مستخدم"
2. املأ البيانات المطلوبة
3. انقر على "حفظ"

## 🐛 استكشاف الأخطاء وإصلاحها

### مشاكل شائعة

#### خطأ في الاتصال بـ Supabase
```
Error: Failed to connect to Supabase
```
**الحل:**
- تأكد من صحة متغيرات البيئة في ملف `.env`
- تحقق من اتصال الإنترنت
- تأكد من أن مشروع Supabase نشط

#### خطأ في المصادقة
```
Error: JWT expired or invalid
```
**الحل:**
- تحقق من صحة Service Role Key
- أعد تشغيل التطبيق

#### مشاكل في التبعيات
```
Error: Package dependencies conflict
```
**الحل:**
```bash
flutter clean
flutter pub get
```

### تسجيل الأخطاء
التطبيق يستخدم Logger لتسجيل الأخطاء. يمكنك مراجعة الـ console لمزيد من التفاصيل.

## 🚀 النشر

### بناء التطبيق للإنتاج

#### Web
```bash
flutter build web --release
```

#### Windows
```bash
flutter build windows --release
```

#### macOS
```bash
flutter build macos --release
```

### متغيرات البيئة للإنتاج
تأكد من إعداد متغيرات البيئة الصحيحة للإنتاج:
- استخدم مفاتيح Supabase الخاصة بالإنتاج
- فعّل HTTPS
- راجع سياسات الأمان

## 🤝 المساهمة

### إرشادات المساهمة
1. Fork المشروع
2. أنشئ branch جديد للميزة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. افتح Pull Request

### معايير الكود
- اتبع Dart style guide
- أضف اختبارات للميزات الجديدة
- تأكد من أن جميع الاختبارات تمر
- أضف توثيق للدوال الجديدة

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك أسئلة:

1. راجع قسم [استكشاف الأخطاء](#-استكشاف-الأخطاء-وإصلاحها)
2. ابحث في [Issues](../../issues) الموجودة
3. أنشئ [Issue جديد](../../issues/new) إذا لم تجد حلاً

## 🔄 التحديثات المستقبلية

### الميزات المخططة
- [ ] إضافة Dashboard مع إحصائيات
- [ ] نظام إشعارات
- [ ] تصدير البيانات
- [ ] Dark Mode
- [ ] دعم لغات متعددة
- [ ] نظام صلاحيات متقدم

### التحسينات التقنية
- [ ] تحسين الأداء
- [ ] إضافة المزيد من الاختبارات
- [ ] تحسين UI/UX
- [ ] إضافة offline support

---

**تم تطوير هذا المشروع بـ ❤️ باستخدام Flutter**
