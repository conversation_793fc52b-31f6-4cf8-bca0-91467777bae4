@echo off
echo ========================================
echo    إصلاح مشكلة انقطاع اتصال الهاتف
echo ========================================
echo.

echo 1. إيقاف ADB server...
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe kill-server
timeout /t 2 /nobreak >nul

echo 2. بدء ADB server...
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe start-server
timeout /t 2 /nobreak >nul

echo 3. فحص الأجهزة المتصلة...
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe devices
echo.

echo 4. فحص Flutter devices...
flutter devices
echo.

echo ========================================
echo تم الانتهاء من إصلاح الاتصال
echo ========================================
pause
