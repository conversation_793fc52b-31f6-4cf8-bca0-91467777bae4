import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../constants/app_constants.dart';
import '../exceptions/app_exceptions.dart' as app_exceptions;
import 'cache_service.dart';
import 'supabase_client.dart';

/// Service for managing users in the admin panel
///
/// This service handles all user-related operations including:
/// - User account management (create, suspend, activate, delete)
/// - Device management
/// - Login attempts tracking
class UsersService {
  final _logger = Logger();
  final _client = SupabaseClientService();
  late final CacheService _cache;

  UsersService() {
    _initCache();
  }

  void _initCache() async {
    _cache = await CacheService.getInstance();
  }

  // Cache keys
  static const String _usersListCacheKey = 'users_list';

  // Use constants from AppConstants
  static const String _userTable = AppConstants.userTable;
  static const String _deviceTable = AppConstants.deviceTable;
  static const String _loginAttemptsTable = AppConstants.loginAttemptsTable;
  static const String _photosTable = AppConstants.photosTable;
  static const String _photosBucket = AppConstants.photosBucket;
  static const String statusActive = AppConstants.statusActive;
  static const String statusSuspended = AppConstants.statusSuspended;
  static const int _maxRetries = AppConstants.maxRetries;

  /// Clear users cache
  Future<void> _clearUsersCache() async {
    try {
      await _cache.remove(_usersListCacheKey);
    } catch (e) {
      _logger.w('Failed to clear users cache: $e');
    }
  }

  /// Get all users with their devices
  ///
  /// Returns a list of users with their associated devices
  /// Uses caching to improve performance
  Future<List<Map<String, dynamic>>> getAllUsers({bool forceRefresh = false}) async {
    try {
      _logger.i('Getting all users');

      // Check cache first unless force refresh is requested
      if (!forceRefresh) {
        final cachedUsers = await _cache.getCachedJsonList(_usersListCacheKey);
        if (cachedUsers != null) {
          _logger.i('Users loaded from cache');
          return cachedUsers;
        }
      }

      final response = await _retryOperation(() => _client.client.from(_userTable).select('''
            *,
            devices (
              id,
              device_name,
              device_id,
              is_active,
              last_login
            )
          ''').order('created_at', ascending: false));

      final users = List<Map<String, dynamic>>.from(response);

      // Cache the results
      await _cache.cacheJsonList(_usersListCacheKey, users, duration: const Duration(minutes: 10));

      _logger.i('Users fetched successfully');
      return users;
    } catch (e, stackTrace) {
      _logger.e('Error in getAllUsers', error: e, stackTrace: stackTrace);

      if (e.toString().contains('JWT')) {
        throw app_exceptions.AuthException('خطأ في المصادقة. الرجاء إعادة تشغيل التطبيق.');
      }
      throw app_exceptions.NetworkException('حدث خطأ في جلب المستخدمين. الرجاء المحاولة مرة أخرى.');
    }
  }

  /// Create a new user
  ///
  /// [nationalId] - User's national ID
  /// [fullName] - User's full name
  /// [password] - User's password
  /// Throws an error if the operation fails
  Future<void> createUser({
    required String nationalId,
    required String fullName,
    required String password,
  }) async {
    try {
      final email = '$<EMAIL>';

      // التحقق من وجود المستخدم
      final existingUser = await _client.client
          .from(_userTable)
          .select()
          .eq('national_id', nationalId)
          .maybeSingle();

      if (existingUser != null) {
        throw 'يوجد مستخدم مسجل برقم الهوية هذا';
      }

      // إنشاء المستخدم في نظام المصادقة
      final authResponse = await _retryOperation(() => _client.client.auth.admin.createUser(
            AdminUserAttributes(
              email: email,
              password: password,
              emailConfirm: true,
            ),
          ));

      if (authResponse.user == null) {
        throw 'فشل في إنشاء المستخدم';
      }

      // إنشاء سجل المستخدم في قاعدة البيانات
      await _retryOperation(() => _client.client.from(_userTable).insert({
            'id': authResponse.user!.id,
            'full_name': fullName,
            'national_id': nationalId,
            'account_status': statusActive,
            'ban_duration': '0',
          }));

      _logger.i('User created successfully with ID: ${authResponse.user!.id}');

      // Clear cache to reflect new user
      await _clearUsersCache();
    } catch (e) {
      _logger.e('Error in createUser', error: e);

      if (e is String) {
        rethrow;
      }
      throw 'حدث خطأ في إنشاء المستخدم. الرجاء المحاولة مرة أخرى.';
    }
  }

  /// Suspend a user's account
  ///
  /// [userId] - ID of the user to suspend
  /// Throws an error if the operation fails
  Future<void> suspendUser(String userId) async {
    try {
      await _retryOperation(() => _client.client.auth.admin.updateUserById(
            userId,
            attributes: AdminUserAttributes(
              banDuration: 'none',
            ),
          ));

      await _retryOperation(() => _client.client
          .from(_userTable)
          .update({'account_status': statusSuspended}).eq('id', userId));

      _logger.i('User suspended successfully: $userId');
    } catch (e) {
      _logger.e('Error suspending user', error: e);
      throw 'فشل في تعليق حساب المستخدم. الرجاء المحاولة مرة أخرى.';
    }
  }

  /// Activate a user's account
  ///
  /// [userId] - ID of the user to activate
  /// Throws an error if the operation fails
  Future<void> activateUser(String userId) async {
    try {
      await _retryOperation(() => _client.client.auth.admin.updateUserById(
            userId,
            attributes: AdminUserAttributes(
              banDuration: '0',
            ),
          ));

      await _retryOperation(() => _client.client
          .from(_userTable)
          .update({'account_status': statusActive}).eq('id', userId));

      _logger.i('User activated successfully: $userId');
    } catch (e) {
      _logger.e('Error activating user', error: e);
      throw 'فشل في تفعيل حساب المستخدم. الرجاء المحاولة مرة أخرى.';
    }
  }

  /// Get login attempts for a specific user
  ///
  /// [userId] - ID of the user
  /// Returns a list of login attempts
  /// Throws an error if the operation fails
  Future<List<Map<String, dynamic>>> getLoginAttempts(String userId) async {
    try {
      final response = await _retryOperation(() => _client.client
          .from(_loginAttemptsTable)
          .select()
          .eq('attempted_user_id', userId)
          .order('created_at', ascending: false)
          .limit(50));

      _logger.i('Login attempts fetched successfully for user: $userId');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _logger.e('Error getting login attempts', error: e);
      throw 'حدث خطأ في جلب محاولات تسجيل الدخول. الرجاء المحاولة مرة أخرى.';
    }
  }

  /// Get all devices for a specific user
  ///
  /// [userId] - ID of the user
  /// Returns a list of devices
  /// Throws an error if the operation fails
  Future<List<Map<String, dynamic>>> getUserDevices(String userId) async {
    try {
      final response = await _retryOperation(() => _client.client
          .from(_deviceTable)
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false));

      _logger.i('Devices fetched successfully for user: $userId');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _logger.e('Error getting user devices', error: e);
      throw 'حدث خطأ في جلب الأجهزة. الرجاء المحاولة مرة أخرى.';
    }
  }

  /// Toggle device status (active/inactive)
  ///
  /// [deviceId] - ID of the device
  /// [isActive] - New status for the device
  /// Throws an error if the operation fails
  Future<void> toggleDeviceStatus(String deviceId, bool isActive) async {
    try {
      await _retryOperation(() =>
          _client.client.from(_deviceTable).update({'is_active': isActive}).eq('id', deviceId));

      _logger.i('Device status updated successfully: $deviceId, isActive: $isActive');
    } catch (e) {
      _logger.e('Error updating device status', error: e);
      throw 'حدث خطأ في تحديث حالة الجهاز. الرجاء المحاولة مرة أخرى.';
    }
  }

  /// Reactivate all devices for a user
  ///
  /// [userId] - ID of the user
  /// Throws an error if the operation fails
  Future<void> reactivateUserDevices(String userId) async {
    try {
      await _retryOperation(() => _client.reactivateUserDevices(userId));
      _logger.i('Successfully reactivated devices for user: $userId');
    } catch (e) {
      _logger.e('Error reactivating user devices', error: e);
      throw 'حدث خطأ في إعادة تفعيل الأجهزة. الرجاء المحاولة مرة أخرى.';
    }
  }

  /// Delete a user and all associated data
  ///
  /// [userId] - ID of the user to delete
  /// This will delete:
  /// - User's photos from storage
  /// - User's photos from database
  /// - User's devices
  /// - User's account
  /// Throws an error if the operation fails
  Future<void> deleteUser(String userId) async {
    try {
      // حذف صور المستخدم
      await _deleteUserPhotos(userId);

      // حذف بيانات المستخدم
      await _deleteUserData(userId);

      _logger.i('User deleted successfully: $userId');
    } catch (e) {
      _logger.e('Error deleting user', error: e);
      throw 'فشل في حذف المستخدم. الرجاء المحاولة مرة أخرى.';
    }
  }

  /// Delete all photos for a user
  Future<void> _deleteUserPhotos(String userId) async {
    try {
      final photos =
          await _client.client.from(_photosTable).select('storage_path').eq('user_id', userId);

      if (photos.isNotEmpty) {
        final storagePaths = photos.map((photo) => photo['storage_path'] as String).toList();

        await _retryOperation(
            () => _client.client.storage.from(_photosBucket).remove(storagePaths));
      }
    } catch (e) {
      _logger.e('Error deleting user photos', error: e);
      rethrow;
    }
  }

  /// Delete all data for a user
  Future<void> _deleteUserData(String userId) async {
    try {
      // حذف البيانات بالترتيب الصحيح لتجنب مشاكل المفاتيح الخارجية
      await _retryOperation(() => _client.client.from(_photosTable).delete().eq('user_id', userId));
      await _retryOperation(() => _client.client.from(_deviceTable).delete().eq('user_id', userId));
      await _retryOperation(() => _client.client.from(_userTable).delete().eq('id', userId));
    } catch (e) {
      _logger.e('Error deleting user data', error: e);
      rethrow;
    }
  }

  /// Retries an operation multiple times before giving up
  Future<T> _retryOperation<T>(
    Future<T> Function() operation, {
    int maxAttempts = _maxRetries,
  }) async {
    int attempts = 0;
    while (attempts < maxAttempts) {
      try {
        return await operation();
      } catch (e) {
        attempts++;
        if (attempts == maxAttempts) rethrow;
        await Future.delayed(Duration(milliseconds: 500 * attempts));
      }
    }
    throw 'Maximum retry attempts reached';
  }
}

final usersServiceProvider = Provider<UsersService>((ref) => UsersService());
