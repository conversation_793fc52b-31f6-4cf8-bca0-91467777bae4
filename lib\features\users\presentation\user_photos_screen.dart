import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../../../core/services/photos_service.dart';

class UserPhotosScreen extends ConsumerStatefulWidget {
  final String userId;
  final String username;

  const UserPhotosScreen({
    super.key,
    required this.userId,
    required this.username,
  });

  @override
  ConsumerState<UserPhotosScreen> createState() => _UserPhotosScreenState();
}

class _UserPhotosScreenState extends ConsumerState<UserPhotosScreen> {
  final List<Map<String, dynamic>> _photos = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 1;
  int _totalPhotos = 0;
  final ScrollController _scrollController = ScrollController();
  final logger = Logger();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadPhotos();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (!_isLoading && _hasMore &&
        _scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8) {
      _loadMorePhotos();
    }
  }

  Future<void> _loadPhotos() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _photos.clear();
      _currentPage = 1;
    });

    try {
      final photosService = ref.read(photosServiceProvider);
      final result = await photosService.getUserPhotos(widget.userId, page: _currentPage);
      
      setState(() {
        _photos.addAll(result.photos);
        _totalPhotos = result.totalCount;
        _hasMore = result.hasMore;
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString())),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMorePhotos() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final photosService = ref.read(photosServiceProvider);
      final result = await photosService.getUserPhotos(
        widget.userId,
        page: _currentPage + 1,
      );

      if (mounted) {
        setState(() {
          _photos.addAll(result.photos);
          _currentPage = result.currentPage;
          _hasMore = result.hasMore;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في تحميل المزيد من الصور'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deletePhoto(Map<String, dynamic> photo) async {
    final storagePath = photo['storage_path'] as String?;
    final id = photo['id'];
    
    if (storagePath == null || id == null) {
      logger.w('Invalid photo data: storagePath=$storagePath, id=$id');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('بيانات الصورة غير صالحة'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذه الصورة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final photosService = ref.read(photosServiceProvider);
      logger.i('Deleting photo with storage_path: $storagePath, id: $id');
      logger.d('Photo data: $photo');
      
      await photosService.deletePhoto(
        storagePath,
        id.toString(),
      );
      
      setState(() {
        _photos.removeWhere((p) => p['id'] == id);
        _totalPhotos--;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الصورة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      logger.e('Error in _deletePhoto: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حذف الصورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _sharePhoto(Map<String, dynamic> photo) async {
    final imageUrl = photo['image_url'] as String?;
    if (imageUrl == null) return;

    try {
      await Clipboard.setData(ClipboardData(text: imageUrl));
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نسخ رابط الصورة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في نسخ رابط الصورة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _downloadPhoto(Map<String, dynamic> photo) async {
    final imageUrl = photo['image_url'] as String?;
    if (imageUrl == null) return;

    try {
      // تحميل الصورة
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode != 200) throw Exception('فشل تحميل الصورة');

      // الحصول على مسار التحميل
      final dir = await getDownloadsDirectory();
      if (dir == null) throw Exception('لم يتم العثور على مجلد التحميلات');

      // إنشاء اسم الملف
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'photo_$timestamp.jpg';
      final filePath = '${dir.path}\\$fileName';

      // حفظ الصورة
      final file = File(filePath);
      await file.writeAsBytes(response.bodyBytes);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حفظ الصورة في: $filePath'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في حفظ الصورة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showPhotoDialog(Map<String, dynamic> photo) {
    final imageUrl = photo['image_url'] as String?;
    if (imageUrl == null) return;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.9,
            maxHeight: MediaQuery.of(context).size.height * 0.9,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                  child: CachedNetworkImage(
                    imageUrl: imageUrl,
                    fit: BoxFit.contain,
                    placeholder: (context, url) => const Center(
                      child: CircularProgressIndicator(),
                    ),
                    errorWidget: (context, url, error) => const Icon(Icons.error),
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () {
                        Navigator.pop(context);
                        _deletePhoto(photo);
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.share),
                      onPressed: () => _sharePhoto(photo),
                    ),
                    IconButton(
                      icon: const Icon(Icons.download),
                      onPressed: () => _downloadPhoto(photo),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('صور ${widget.username}'),
        actions: [
          if (_totalPhotos > 0)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Center(
                child: Text(
                  '$_totalPhotos صورة',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
            ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadPhotos,
        child: _photos.isEmpty && !_isLoading
            ? const Center(child: Text('لا توجد صور'))
            : GridView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(8),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  mainAxisSpacing: 8,
                  crossAxisSpacing: 8,
                ),
                itemCount: _photos.length + (_hasMore ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == _photos.length) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  final photo = _photos[index];
                  final thumbnailUrl = photo['thumbnail_url'] as String?;
                  
                  return GestureDetector(
                    onTap: () => _showPhotoDialog(photo),
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: CachedNetworkImage(
                              imageUrl: thumbnailUrl ?? '',
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: Colors.grey.shade200,
                                child: const Center(
                                  child: CircularProgressIndicator(),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: Colors.grey.shade200,
                                child: const Icon(Icons.error),
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.bottomCenter,
                                end: Alignment.topCenter,
                                colors: [
                                  Colors.black.withAlpha(179), // 0.7 opacity
                                  Colors.transparent,
                                ],
                              ),
                            ),
                            child: Text(
                              _formatDateTime(photo['date_time'] as String),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
      ),
    );
  }

  String _formatDateTime(String dateTime) {
    final date = DateTime.parse(dateTime).toLocal();
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return 'قبل ${difference.inMinutes} دقيقة';
      }
      return 'قبل ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'قبل ${difference.inDays} يوم';
    } else {
      return '${date.year}/${date.month}/${date.day}';
    }
  }
}
