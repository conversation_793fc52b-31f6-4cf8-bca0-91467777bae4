-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables if they exist
DROP TABLE IF EXISTS photos CASCADE;
DROP TABLE IF EXISTS activity_logs CASCADE;
DROP TABLE IF EXISTS login_attempts CASCADE;
DROP TABLE IF EXISTS devices CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS admins CASCADE;

-- Delete all existing users from auth.users
DELETE FROM auth.users;

-- Create admins table
CREATE TABLE admins (
    id UUID REFERENCES auth.users PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create users table
CREATE TABLE users (
    id UUID REFERENCES auth.users PRIMARY KEY,
    national_id TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    account_status TEXT NOT NULL DEFAULT 'active',
    ban_duration TEXT DEFAULT '0',
    first_login BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create devices table
CREATE TABLE devices (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users NOT NULL,
    device_id TEXT NOT NULL,
    device_name TEXT,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(user_id, device_id)
);

-- Create login_attempts table
CREATE TABLE login_attempts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    attempted_user_id UUID REFERENCES users NOT NULL,
    device_id TEXT,
    device_name TEXT,
    ip_address TEXT,
    success BOOLEAN NOT NULL,
    failure_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create activity_logs table
CREATE TABLE activity_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users NOT NULL,
    action TEXT NOT NULL,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create photos table
CREATE TABLE IF NOT EXISTS public.photos (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL
);

-- تحديث جدول الصور
ALTER TABLE public.photos 
ADD COLUMN IF NOT EXISTS username TEXT,
ADD COLUMN IF NOT EXISTS location TEXT,
ADD COLUMN IF NOT EXISTS date_time TEXT,
ADD COLUMN IF NOT EXISTS file_name TEXT,
ADD COLUMN IF NOT EXISTS storage_path TEXT;

-- إنشاء index للبحث السريع عن صور المستخدم
CREATE INDEX IF NOT EXISTS photos_user_id_idx ON public.photos(user_id);

-- تحديث السياسات
DROP POLICY IF EXISTS "Users can view all photos" ON public.photos;
CREATE POLICY "Users can view all photos"
    ON public.photos FOR SELECT
    TO authenticated
    USING (true);

DROP POLICY IF EXISTS "Users can insert their own photos" ON public.photos;
CREATE POLICY "Users can insert their own photos"
    ON public.photos FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own photos" ON public.photos;
CREATE POLICY "Users can update their own photos"
    ON public.photos FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own photos" ON public.photos;
CREATE POLICY "Users can delete their own photos"
    ON public.photos FOR DELETE
    TO authenticated
    USING (auth.uid() = user_id);

-- Function to verify device for login
CREATE OR REPLACE FUNCTION verify_device_for_login(
    p_user_id UUID,
    p_device_id TEXT,
    p_device_name TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_first_login BOOLEAN;
    v_device_exists BOOLEAN;
    v_device_active BOOLEAN;
    v_other_user_exists BOOLEAN;
BEGIN
    -- Check if device is registered to another user
    SELECT EXISTS (
        SELECT 1 FROM devices 
        WHERE device_id = p_device_id 
        AND user_id != p_user_id
        AND is_active = true
    ) INTO v_other_user_exists;

    -- If device is registered to another user, deny access
    IF v_other_user_exists THEN
        -- Log the attempt
        INSERT INTO login_attempts (
            attempted_user_id,
            device_id,
            device_name,
            success,
            failure_reason
        )
        VALUES (
            p_user_id,
            p_device_id,
            p_device_name,
            false,
            'Device is registered to another user'
        );
        RETURN false;
    END IF;

    -- Check if this is user's first login
    SELECT first_login INTO v_first_login
    FROM users
    WHERE id = p_user_id;

    -- If it's first login, register the device
    IF v_first_login THEN
        -- Check if device already exists for this user
        SELECT EXISTS (
            SELECT 1 FROM devices 
            WHERE user_id = p_user_id 
            AND device_id = p_device_id
        ) INTO v_device_exists;

        -- Only insert if device doesn't exist
        IF NOT v_device_exists THEN
            INSERT INTO devices (user_id, device_id, device_name)
            VALUES (p_user_id, p_device_id, p_device_name);
        ELSE
            -- Update device if it exists
            UPDATE devices 
            SET is_active = true,
                device_name = p_device_name,
                last_login = now()
            WHERE user_id = p_user_id 
            AND device_id = p_device_id;
        END IF;
        
        -- Update user's first_login status
        UPDATE users SET first_login = false
        WHERE id = p_user_id;

        -- Log successful registration
        INSERT INTO login_attempts (
            attempted_user_id,
            device_id,
            device_name,
            success,
            failure_reason
        )
        VALUES (
            p_user_id,
            p_device_id,
            p_device_name,
            true,
            'First login - device registered'
        );
        
        RETURN true;
    END IF;

    -- Check if device exists and is active for this user
    SELECT EXISTS (
        SELECT 1 FROM devices 
        WHERE user_id = p_user_id 
        AND device_id = p_device_id
        AND is_active = true
    ) INTO v_device_active;

    -- Log the attempt
    INSERT INTO login_attempts (
        attempted_user_id,
        device_id,
        device_name,
        success,
        failure_reason
    )
    VALUES (
        p_user_id,
        p_device_id,
        p_device_name,
        v_device_active,
        CASE 
            WHEN NOT v_device_active THEN 'Device not registered or inactive for this user'
            ELSE NULL
        END
    );

    -- Update last_login if successful
    IF v_device_active THEN
        UPDATE devices 
        SET last_login = now()
        WHERE user_id = p_user_id 
        AND device_id = p_device_id;
    END IF;

    RETURN v_device_active;
END;
$$;

-- Function to reactivate user's devices
CREATE OR REPLACE FUNCTION reactivate_user_devices(
    p_user_id UUID
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Reset user's first_login status
    UPDATE users 
    SET first_login = true
    WHERE id = p_user_id;

    -- Deactivate all existing devices
    UPDATE devices 
    SET is_active = false
    WHERE user_id = p_user_id;

    -- Log the action
    INSERT INTO activity_logs (user_id, action, details)
    VALUES (
        p_user_id,
        'devices_reset',
        jsonb_build_object(
            'timestamp', now(),
            'action', 'All devices deactivated and user reset to first login state'
        )
    );
END;
$$;

-- Create function to test service role access
CREATE OR REPLACE FUNCTION get_service_role()
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF auth.jwt()->>'role' = 'service_role' THEN
    RETURN jsonb_build_object(
      'role', auth.jwt()->>'role',
      'success', true
    );
  ELSE
    RETURN jsonb_build_object(
      'role', auth.jwt()->>'role',
      'success', false
    );
  END IF;
END;
$$;

-- Grant permissions for service_role
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL ON ALL ROUTINES IN SCHEMA public TO service_role;

-- Enable RLS for all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;

-- Create policies for users
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON users;
CREATE POLICY "Enable read access for authenticated users"
ON users FOR SELECT
TO authenticated
USING (
    -- Allow users to read their own data
    auth.uid() = id
    OR
    -- Allow users to read any user data (needed for username lookup)
    true
);

-- Create policies for devices
DROP POLICY IF EXISTS "Enable device access for owners" ON devices;
CREATE POLICY "Enable device access for owners"
ON devices
FOR ALL
TO authenticated
USING (
    -- Allow users to access their own devices
    auth.uid() = user_id
);

-- Disable RLS for admin tables
ALTER TABLE admins DISABLE ROW LEVEL SECURITY;
ALTER TABLE login_attempts DISABLE ROW LEVEL SECURITY;
ALTER TABLE activity_logs DISABLE ROW LEVEL SECURITY;

-- Grant additional permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- إنشاء bucket للصور إذا لم يكن موجوداً
INSERT INTO storage.buckets (id, name, public)
SELECT 'photos', 'photos', true
WHERE NOT EXISTS (
    SELECT 1 FROM storage.buckets WHERE id = 'photos'
);

-- تحديث سياسات الوصول للـ bucket
DROP POLICY IF EXISTS "Public Access" ON storage.objects;
CREATE POLICY "Public Access"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'photos');

DROP POLICY IF EXISTS "Authenticated users can upload photos" ON storage.objects;
CREATE POLICY "Authenticated users can upload photos"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'photos' 
    AND (storage.foldername(name))[1] = 'users' 
    AND (storage.foldername(name))[2] = auth.uid()::text
);

DROP POLICY IF EXISTS "Users can update their own photos" ON storage.objects;
CREATE POLICY "Users can update their own photos"
ON storage.objects FOR UPDATE
TO authenticated
USING (
    bucket_id = 'photos'
    AND (storage.foldername(name))[1] = 'users'
    AND (storage.foldername(name))[2] = auth.uid()::text
);

DROP POLICY IF EXISTS "Users can delete their own photos" ON storage.objects;
CREATE POLICY "Users can delete their own photos"
ON storage.objects FOR DELETE
TO authenticated
USING (
    bucket_id = 'photos'
    AND (storage.foldername(name))[1] = 'users'
    AND (storage.foldername(name))[2] = auth.uid()::text
);
