import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:moon_memory_admin/main.dart';

void main() {
  group('Main App Tests', () {
    testWidgets('AdminApp should build without errors', (WidgetTester tester) async {
      // Create a test app with ProviderScope
      await tester.pumpWidget(
        const ProviderScope(
          child: AdminApp(),
        ),
      );

      // Verify that the app builds
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('AdminApp should have correct title', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: AdminApp(),
        ),
      );

      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      expect(materialApp.title, equals('Moon Memory Admin'));
    });

    testWidgets('AdminApp should have RTL direction', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: AdminApp(),
        ),
      );

      // Check if Directionality widget exists with RTL
      expect(find.byWidgetPredicate((widget) =>
        widget is Directionality &&
        widget.textDirection == TextDirection.rtl
      ), findsOneWidget);
    });

    testWidgets('AdminApp should not show debug banner', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: AdminApp(),
        ),
      );

      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      expect(materialApp.debugShowCheckedModeBanner, isFalse);
    });
  });
}
