# دليل استكشاف الأخطاء وإصلاحها

## مشكلة انقطاع اتصال الهاتف

### الأعراض
- الهاتف يظهر متصل ثم ينقطع فجأة
- `flutter devices` لا يظهر الهاتف
- رسالة خطأ: "No supported devices found"

### الأسباب المحتملة
1. **مشاكل USB**: كابل USB تالف أو منفذ USB لا يعمل بشكل صحيح
2. **إعدادات الهاتف**: USB Debugging معطل أو تغيرت إعدادات المطور
3. **مشاكل ADB**: ADB server معلق أو متوقف
4. **تداخل البرامج**: برامج أخرى تستخدم ADB
5. **إعدادات الطاقة**: Windows يوقف منفذ USB لتوفير الطاقة

### الحلول

#### 1. إع<PERSON>ة تشغيل ADB Server
```bash
# إيقاف ADB
adb kill-server

# بدء ADB
adb start-server

# فحص الأجهزة
adb devices
```

#### 2. استخدام Scripts الجاهزة
```bash
# Windows Batch
scripts/fix_adb_connection.bat

# PowerShell
scripts/fix_adb_connection.ps1
```

#### 3. فحص إعدادات الهاتف
- تأكد من تفعيل **Developer Options**
- تأكد من تفعيل **USB Debugging**
- جرب تغيير **USB Configuration** إلى:
  - File Transfer (MTP)
  - أو PTP
  - أو Charging only

#### 4. فحص الكابل والمنفذ
- جرب كابل USB مختلف
- جرب منفذ USB مختلف
- تأكد من أن الكابل يدعم نقل البيانات وليس الشحن فقط

#### 5. إعدادات Windows
```powershell
# تعطيل إيقاف USB لتوفير الطاقة
# Device Manager > USB Root Hub > Properties > Power Management
# إلغاء تحديد "Allow the computer to turn off this device"
```

#### 6. إعادة تثبيت USB Drivers
1. افتح Device Manager
2. ابحث عن الهاتف تحت "Android Device" أو "Portable Devices"
3. انقر بالزر الأيمن واختر "Uninstall device"
4. افصل الهاتف وأعد توصيله

### حلول متقدمة

#### 1. تغيير منفذ ADB
```bash
# تغيير منفذ ADB إلى منفذ مختلف
adb -P 5038 start-server
```

#### 2. استخدام ADB عبر WiFi
```bash
# على الهاتف المتصل عبر USB
adb tcpip 5555

# الحصول على IP الهاتف
adb shell ip route

# الاتصال عبر WiFi
adb connect <PHONE_IP>:5555

# فصل USB والاستمرار عبر WiFi
```

#### 3. فحص العمليات المتداخلة
```bash
# فحص العمليات التي تستخدم ADB
netstat -ano | findstr :5037
tasklist | findstr adb
```

### مراقبة الاتصال

#### Script مراقبة مستمرة
```powershell
# تشغيل مراقبة الاتصال كل 10 ثوان
. .\scripts\fix_adb_connection.ps1
Watch-AdbConnection -IntervalSeconds 10
```

### نصائح للوقاية

1. **استخدم كابل USB عالي الجودة**
2. **تجنب تحريك الهاتف أثناء التطوير**
3. **أبقِ الهاتف مشحون (أكثر من 20%)**
4. **تجنب استخدام USB hubs**
5. **أغلق البرامج الأخرى التي قد تستخدم ADB**

### إعدادات Flutter المفيدة

```bash
# زيادة timeout للأجهزة
flutter devices --device-timeout=30

# تشغيل مع hot reload
flutter run --hot

# تشغيل مع verbose logging
flutter run -v
```

### فحص سريع للمشكلة

```bash
# 1. فحص ADB
adb version

# 2. فحص الأجهزة
adb devices -l

# 3. فحص Flutter
flutter doctor -v

# 4. فحص الاتصال
flutter devices
```

### إذا استمرت المشكلة

1. **أعد تشغيل الكمبيوتر والهاتف**
2. **جرب هاتف مختلف للتأكد من المشكلة**
3. **تحديث Android SDK Platform Tools**
4. **تحديث Flutter إلى أحدث إصدار**

---

## مشاكل أخرى شائعة

### مشكلة عدم ظهور المستخدمين

#### الأعراض
- التطبيق يعمل لكن لا يظهر مستخدمين
- رسائل خطأ في الاتصال بقاعدة البيانات

#### الحلول
1. **فحص متغيرات البيئة**
2. **فحص اتصال الإنترنت**
3. **فحص إعدادات Supabase**
4. **استخدام زر التشخيص في التطبيق**

### مشكلة بناء التطبيق

#### الأعراض
- أخطاء في البناء
- تبعيات مفقودة

#### الحلول
```bash
# تنظيف المشروع
flutter clean

# تحديث التبعيات
flutter pub get

# إعادة البناء
flutter run
```
