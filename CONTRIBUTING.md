# إرشادات المساهمة

نرحب بمساهماتكم في تطوير Moon Memory Admin Panel! هذا الدليل سيساعدكم على فهم كيفية المساهمة بفعالية.

## 🚀 البدء السريع

### 1. إعداد البيئة التطويرية
```bash
# استنساخ المشروع
git clone <repository-url>
cd moon_memory_admin

# تثبيت التبعيات
flutter pub get

# إعداد متغيرات البيئة
cp .env.example .env
# قم بتعديل .env بالقيم الصحيحة

# تشغيل الاختبارات للتأكد من سلامة الإعداد
flutter test
```

### 2. فهم بنية المشروع
```
lib/
├── core/           # الملفات الأساسية والمشتركة
├── features/       # ميزات التطبيق المختلفة
├── shared/         # المكونات المشتركة
└── main.dart       # نقطة البداية
```

## 📋 أنواع المساهمات

### 🐛 إصلاح الأخطاء (Bug Fixes)
- ابحث أولاً في Issues الموجودة
- أنشئ Issue جديد إذا لم يكن موجوداً
- اربط Pull Request بالـ Issue

### ✨ ميزات جديدة (New Features)
- ناقش الميزة أولاً في Issue
- تأكد من توافقها مع رؤية المشروع
- اتبع معايير التصميم الموجودة

### 📚 تحسين التوثيق
- تحسين README
- إضافة تعليقات للكود
- كتابة أمثلة للاستخدام

### 🧪 إضافة اختبارات
- اختبارات للميزات الجديدة
- تحسين تغطية الاختبارات الموجودة
- اختبارات الأداء

## 🔧 معايير الكود

### Dart Style Guide
```dart
// ✅ جيد
class UserService {
  final Logger _logger = Logger();
  
  Future<List<User>> getUsers() async {
    try {
      // implementation
    } catch (e) {
      _logger.e('Error getting users', error: e);
      rethrow;
    }
  }
}

// ❌ سيء
class userservice {
  getusers() {
    // no error handling
  }
}
```

### تسمية الملفات والمجلدات
- استخدم `snake_case` للملفات: `user_service.dart`
- استخدم `PascalCase` للكلاسات: `UserService`
- استخدم `camelCase` للمتغيرات: `userName`

### التعليقات والتوثيق
```dart
/// Service for managing users in the admin panel
/// 
/// This service handles all user-related operations including:
/// - User account management
/// - Device management
/// - Login attempts tracking
class UserService {
  /// Get all users with their devices
  /// 
  /// Returns a list of users with their associated devices.
  /// Throws [NetworkException] if the operation fails.
  Future<List<Map<String, dynamic>>> getAllUsers() async {
    // implementation
  }
}
```

## 🧪 معايير الاختبارات

### كتابة الاختبارات
```dart
group('UserService', () {
  late UserService userService;
  
  setUp(() {
    userService = UserService();
  });
  
  group('getAllUsers', () {
    test('should return list of users when successful', () async {
      // Arrange
      // Act
      final result = await userService.getAllUsers();
      
      // Assert
      expect(result, isA<List<Map<String, dynamic>>>());
    });
    
    test('should throw NetworkException when network fails', () async {
      // Test error scenarios
    });
  });
});
```

### تشغيل الاختبارات
```bash
# جميع الاختبارات
flutter test

# اختبار محدد
flutter test test/core/services/user_service_test.dart

# مع تقرير التغطية
flutter test --coverage
```

## 📝 عملية المساهمة

### 1. إنشاء Issue
- استخدم القوالب المتوفرة
- اكتب وصفاً واضحاً للمشكلة أو الميزة
- أضف labels مناسبة

### 2. إنشاء Branch
```bash
# للميزات الجديدة
git checkout -b feature/user-management-enhancement

# لإصلاح الأخطاء
git checkout -b fix/login-validation-bug

# للتوثيق
git checkout -b docs/api-documentation
```

### 3. كتابة الكود
- اتبع معايير الكود المذكورة أعلاه
- أضف اختبارات للكود الجديد
- تأكد من أن جميع الاختبارات تمر

### 4. Commit Messages
```bash
# ✅ جيد
git commit -m "feat: add user search functionality"
git commit -m "fix: resolve login validation issue"
git commit -m "docs: update API documentation"

# ❌ سيء
git commit -m "update"
git commit -m "fix bug"
```

### 5. إنشاء Pull Request
- استخدم القالب المتوفر
- اربط بالـ Issue ذات الصلة
- أضف وصفاً واضحاً للتغييرات
- أضف screenshots إذا كانت التغييرات تؤثر على UI

## 🔍 مراجعة الكود

### معايير المراجعة
- ✅ الكود يتبع معايير المشروع
- ✅ الاختبارات موجودة وتمر
- ✅ التوثيق محدث
- ✅ لا توجد تحذيرات أو أخطاء
- ✅ الأداء مقبول

### عملية المراجعة
1. مراجعة تلقائية (CI/CD)
2. مراجعة من قبل maintainer
3. طلب تعديلات إذا لزم الأمر
4. موافقة ودمج

## 🚫 ما يجب تجنبه

- ❌ رفع مفاتيح API أو كلمات مرور
- ❌ كسر الـ breaking changes دون مناقشة
- ❌ إضافة تبعيات غير ضرورية
- ❌ تجاهل معايير الكود
- ❌ عدم كتابة اختبارات

## 🆘 طلب المساعدة

إذا كنت بحاجة لمساعدة:

1. راجع التوثيق الموجود
2. ابحث في Issues المغلقة
3. أنشئ Issue جديد مع label "question"
4. انضم إلى مناقشات المجتمع

## 🎉 شكراً لمساهمتكم!

كل مساهمة، مهما كانت صغيرة، تساعد في تحسين المشروع. نقدر وقتكم وجهدكم!

---

**Happy Coding! 🚀**
