import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/config/supabase_config.dart';
import '../../../core/services/users_service.dart';
import 'add_user_screen.dart';
import 'user_details_screen.dart';
import 'user_photos_screen.dart';

class UsersListScreen extends ConsumerStatefulWidget {
  const UsersListScreen({super.key});

  @override
  ConsumerState<UsersListScreen> createState() => _UsersListScreenState();
}

class _UsersListScreenState extends ConsumerState<UsersListScreen> {
  List<Map<String, dynamic>> _users = [];
  List<Map<String, dynamic>> _filteredUsers = [];
  bool _isLoading = true;
  String? _error;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterUsers(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredUsers = _users;
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();
    setState(() {
      _filteredUsers = _users.where((user) {
        final fullName = (user['full_name'] ?? '').toString().toLowerCase();
        final username = (user['username'] ?? '').toString().toLowerCase();
        final nationalId = (user['national_id'] ?? '').toString().toLowerCase();
        final email = (user['email'] ?? '').toString().toLowerCase();

        return fullName.contains(lowercaseQuery) ||
            username.contains(lowercaseQuery) ||
            nationalId.contains(lowercaseQuery) ||
            email.contains(lowercaseQuery);
      }).toList();
    });
  }

  Future<void> _loadUsers() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final usersService = ref.read(usersServiceProvider);
      final users = await usersService.getAllUsers();

      if (!mounted) return;
      setState(() {
        _users = users;
        _filteredUsers = users;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      String errorMessage = e.toString();
      if (errorMessage.contains('SocketException') ||
          errorMessage.contains('Network is unreachable')) {
        errorMessage =
            'لا يمكن الاتصال بالخادم. الرجاء التحقق من اتصال الإنترنت والمحاولة مرة أخرى.';
      }

      setState(() {
        _error = errorMessage;
        _isLoading = false;
      });
    }
  }

  void _showDebugInfo() async {
    String debugInfo = 'تشخيص النظام:\n\n';

    try {
      // فحص متغيرات البيئة
      debugInfo += '1. متغيرات البيئة:\n';
      try {
        SupabaseConfig.supabaseUrl;
        debugInfo += '   ✅ SUPABASE_URL موجود\n';
      } catch (e) {
        debugInfo += '   ❌ SUPABASE_URL مفقود: $e\n';
      }

      // فحص اتصال قاعدة البيانات
      debugInfo += '\n2. اتصال قاعدة البيانات:\n';
      try {
        final usersService = ref.read(usersServiceProvider);
        final users = await usersService.getAllUsers(forceRefresh: true);
        debugInfo += '   ✅ الاتصال ناجح\n';
        debugInfo += '   📊 عدد المستخدمين: ${users.length}\n';
      } catch (e) {
        debugInfo += '   ❌ فشل الاتصال: $e\n';
      }
    } catch (e) {
      debugInfo += '\n❌ خطأ في التشخيص: $e\n';
    }

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تشخيص النظام'),
        content: SingleChildScrollView(
          child: Text(
            debugInfo,
            style: const TextStyle(fontFamily: 'monospace'),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Future<void> _handleAddUser() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => const AddUserScreen(),
      ),
    );

    if (result == true) {
      _loadUsers();
    }
  }

  Future<void> _toggleUserStatus(String userId, bool isSuspended) async {
    try {
      final usersService = ref.read(usersServiceProvider);
      if (isSuspended) {
        await usersService.activateUser(userId);
      } else {
        await usersService.suspendUser(userId);
      }
      _loadUsers();
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    }
  }

  void _showDevicesDialog(Map<String, dynamic> user) async {
    final usersService = ref.read(usersServiceProvider);
    final devices = await usersService.getUserDevices(user['id']);
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('أجهزة ${user['full_name']}'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: devices.length,
            itemBuilder: (context, index) {
              final device = devices[index];
              return ListTile(
                title: Text(device['device_name'] ?? 'جهاز غير معروف'),
                subtitle: Text(device['device_id']),
                trailing: Chip(
                  label: Text(device['is_active'] ? 'نشط' : 'غير نشط'),
                  backgroundColor: device['is_active'] ? Colors.green : Colors.grey,
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _reactivateDevices(String userId) async {
    try {
      final usersService = ref.read(usersServiceProvider);
      await usersService.reactivateUserDevices(userId);
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم إعادة تنشيط الأجهزة بنجاح')),
      );
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل في إعادة تنشيط الأجهزة: $e')),
      );
    }
  }

  Future<void> _deleteUser(String userId, String username) async {
    // عرض نافذة تأكيد الحذف
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد حذف الحساب'),
        content: Text('هل أنت متأكد من حذف حساب "$username"؟\nلا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final usersService = ref.read(usersServiceProvider);
      await usersService.deleteUser(userId);

      if (!mounted) return;

      // إغلاق النافذة المنبثقة
      Navigator.pop(context);

      // تحديث القائمة
      _loadUsers();

      // عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حذف الحساب بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(e.toString()),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showUserActions(Map<String, dynamic> user) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // صورة المستخدم
              CircleAvatar(
                radius: 40,
                backgroundColor: Colors.grey[200],
                child: Text(
                  (user['full_name'] ?? user['username'] ?? '').substring(0, 1).toUpperCase(),
                  style: const TextStyle(
                    fontSize: 30,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // اسم المستخدم
              Text(
                user['full_name'] ?? user['username'] ?? 'مستخدم',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                user['email'] ?? '',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 24),

              // الأزرار
              Wrap(
                spacing: 16,
                runSpacing: 16,
                alignment: WrapAlignment.center,
                children: [
                  _buildActionCard(
                    icon: Icons.info,
                    label: 'التفاصيل',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => UserDetailsScreen(
                            userId: user['id'],
                            userName: user['full_name'],
                          ),
                        ),
                      );
                    },
                  ),
                  _buildActionCard(
                    icon: Icons.devices,
                    label: 'الأجهزة',
                    onTap: () {
                      Navigator.pop(context);
                      _showDevicesDialog(user);
                    },
                  ),
                  _buildActionCard(
                    icon: Icons.refresh,
                    label: 'تنشيط الأجهزة',
                    onTap: () {
                      Navigator.pop(context);
                      _reactivateDevices(user['id']);
                    },
                  ),
                  _buildActionCard(
                    icon: Icons.photo_library,
                    label: 'الصور',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => UserPhotosScreen(
                            userId: user['id'],
                            username: user['username'] ?? 'مستخدم',
                          ),
                        ),
                      );
                    },
                  ),
                  _buildActionCard(
                    icon: (user['is_suspended'] ?? false) ? Icons.check_circle : Icons.block,
                    label: (user['is_suspended'] ?? false) ? 'تفعيل الحساب' : 'تعليق الحساب',
                    color: (user['is_suspended'] ?? false) ? Colors.green : Colors.red,
                    onTap: () {
                      Navigator.pop(context);
                      _toggleUserStatus(
                        user['id'],
                        user['is_suspended'] ?? false,
                      );
                    },
                  ),
                  _buildActionCard(
                    icon: Icons.delete_forever,
                    label: 'حذف الحساب',
                    color: Colors.red,
                    onTap: () => _deleteUser(
                      user['id'],
                      user['full_name'] ?? user['username'] ?? 'مستخدم',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    Color? color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(10),
      child: Container(
        width: 100,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 30,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12,
                color: color ?? Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المستخدمين'),
        actions: [
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: () {
              _showDebugInfo();
            },
            tooltip: 'تشخيص النظام',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUsers,
            tooltip: 'تحديث القائمة',
          ),
          IconButton(
            icon: const Icon(Icons.person_add),
            onPressed: _handleAddUser,
            tooltip: 'إضافة مستخدم',
          ),
        ],
      ),
      body: Column(
        children: [
          // حقل البحث
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              onChanged: _filterUsers,
              decoration: InputDecoration(
                hintText: 'البحث بالاسم أو رقم الهوية...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterUsers('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
          ),

          // عدد النتائج
          if (_searchController.text.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  Text(
                    'نتائج البحث: ${_filteredUsers.length}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),

          // قائمة المستخدمين
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _error != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(_error!),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadUsers,
                              child: const Text('إعادة المحاولة'),
                            ),
                          ],
                        ),
                      )
                    : _filteredUsers.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.search_off,
                                  size: 64,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _searchController.text.isEmpty
                                      ? 'لا يوجد مستخدمين'
                                      : 'لا توجد نتائج للبحث',
                                  style: Theme.of(context).textTheme.titleMedium,
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _filteredUsers.length,
                            itemBuilder: (context, index) {
                              final user = _filteredUsers[index];
                              return Card(
                                margin: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                child: ListTile(
                                  leading: CircleAvatar(
                                    backgroundColor: Colors.grey[200],
                                    child: Text(
                                      (user['full_name'] ?? user['username'] ?? '')
                                          .substring(0, 1)
                                          .toUpperCase(),
                                    ),
                                  ),
                                  title: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          user['full_name'] ?? user['username'] ?? 'مستخدم',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.grey[200],
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Text(
                                          'رقم الهوية: ${user['national_id'] ?? 'غير محدد'}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey[700],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  subtitle: Text(user['email'] ?? ''),
                                  onTap: () => _showUserActions(user),
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
    );
  }
}
