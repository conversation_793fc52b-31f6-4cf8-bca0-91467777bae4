import 'package:intl/intl.dart';
import '../constants/app_constants.dart';

/// Date and time utilities
class DateUtils {
  static final _dateTimeFormatter = DateFormat(AppConstants.dateTimeFormat);
  static final _dateFormatter = DateFormat(AppConstants.dateFormat);
  static final _arabicDateFormatter = DateFormat('dd/MM/yyyy', 'ar');
  static final _arabicDateTimeFormatter = DateFormat('dd/MM/yyyy HH:mm', 'ar');

  /// Format DateTime to string with default format
  static String formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '-';
    return _dateTimeFormatter.format(dateTime);
  }

  /// Format DateTime to date only
  static String formatDate(DateTime? dateTime) {
    if (dateTime == null) return '-';
    return _dateFormatter.format(dateTime);
  }

  /// Format DateTime to Arabic format
  static String formatDateTimeArabic(DateTime? dateTime) {
    if (dateTime == null) return '-';
    return _arabicDateTimeFormatter.format(dateTime);
  }

  /// Format DateTime to Arabic date only
  static String formatDateArabic(DateTime? dateTime) {
    if (dateTime == null) return '-';
    return _arabicDateFormatter.format(dateTime);
  }

  /// Get relative time (e.g., "منذ 5 دقائق")
  static String getRelativeTime(DateTime? dateTime) {
    if (dateTime == null) return '-';
    
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  /// Check if date is today
  static bool isToday(DateTime? dateTime) {
    if (dateTime == null) return false;
    final now = DateTime.now();
    return dateTime.year == now.year &&
           dateTime.month == now.month &&
           dateTime.day == now.day;
  }

  /// Check if date is yesterday
  static bool isYesterday(DateTime? dateTime) {
    if (dateTime == null) return false;
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return dateTime.year == yesterday.year &&
           dateTime.month == yesterday.month &&
           dateTime.day == yesterday.day;
  }

  /// Get smart date format (Today, Yesterday, or date)
  static String getSmartDateFormat(DateTime? dateTime) {
    if (dateTime == null) return '-';
    
    if (isToday(dateTime)) {
      return 'اليوم ${DateFormat('HH:mm').format(dateTime)}';
    } else if (isYesterday(dateTime)) {
      return 'أمس ${DateFormat('HH:mm').format(dateTime)}';
    } else {
      return formatDateTimeArabic(dateTime);
    }
  }

  /// Parse string to DateTime
  static DateTime? parseDateTime(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    
    try {
      return DateTime.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Get age from birth date
  static int? getAge(DateTime? birthDate) {
    if (birthDate == null) return null;
    
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    
    return age;
  }
}
