import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import '../config/supabase_config.dart';

class SupabaseClientService {
  static final _logger = Logger();
  late final SupabaseClient _client;
  
  SupabaseClientService() {
    _client = SupabaseClient(
      SupabaseConfig.supabaseUrl,
      SupabaseConfig.supabaseServiceKey,
      headers: {'apikey': SupabaseConfig.supabaseServiceKey},
    );
  }

  SupabaseClient get client => _client;

  /// Verify device for login and register if first time
  Future<bool> verifyDeviceForLogin(String userId, String deviceId, String deviceName) async {
    try {
      final response = await _client
          .rpc('verify_device_for_login', params: {
            'p_user_id': userId,
            'p_device_id': deviceId,
            'p_device_name': deviceName,
          });
      return response as bool;
    } catch (e) {
      _logger.e('Error verifying device: $e');
      return false;
    }
  }

  /// Reactivate user's devices (admin only)
  Future<void> reactivateUserDevices(String userId) async {
    try {
      await _client.rpc('reactivate_user_devices', params: {
        'p_user_id': userId,
      });
      _logger.i('Successfully reactivated devices for user: $userId');
    } catch (e) {
      _logger.e('Error reactivating devices: $e');
      throw Exception('Failed to reactivate devices');
    }
  }

  /// Get user's registered devices
  Future<List<Map<String, dynamic>>> getUserDevices(String userId) async {
    try {
      final response = await _client
          .from('devices')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _logger.e('Error getting user devices: $e');
      return [];
    }
  }
}
