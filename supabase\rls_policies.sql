-- تعطيل RLS على جدول users
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- حذف السياسات القديمة إن وجدت
DROP POLICY IF EXISTS "Enable read access for all users" ON users;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON users;
DROP POLICY IF EXISTS "Enable update for users based on id" ON users;
DROP POLICY IF EXISTS "Enable delete for users based on id" ON users;

-- منح صلاحيات كاملة لدور service_role
GRANT ALL ON users TO service_role;

-- إعادة تفعيل RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- إضافة سياسات جديدة
CREATE POLICY "Enable read access for service_role"
ON users FOR SELECT
TO service_role
USING (true);

CREATE POLICY "Enable insert for service_role"
ON users FOR INSERT
TO service_role
WITH CHECK (true);

CREATE POLICY "Enable update for service_role"
ON users FOR UPDATE
TO service_role
USING (true)
WITH CHECK (true);

CREATE POLICY "Enable delete for service_role"
ON users FOR DELETE
TO service_role
USING (true);

-- Enable RLS
ALTER TABLE devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow all authenticated users to read users table" ON users;
DROP POLICY IF EXISTS "Users can read their own data" ON users;
DROP POLICY IF EXISTS "Users can read by national ID during login" ON users;
DROP POLICY IF EXISTS "Users can read their own devices" ON devices;
DROP POLICY IF EXISTS "Users can insert their own devices" ON devices;
DROP POLICY IF EXISTS "Users can update their own devices" ON devices;
DROP POLICY IF EXISTS "Users can delete their own devices" ON devices;
DROP POLICY IF EXISTS "Users can read their own photos" ON photos;
DROP POLICY IF EXISTS "Users can insert their own photos" ON photos;
DROP POLICY IF EXISTS "Users can update their own photos" ON photos;
DROP POLICY IF EXISTS "Users can delete their own photos" ON photos;
DROP POLICY IF EXISTS "Enable read access for service_role" ON users;
DROP POLICY IF EXISTS "Users can view their own devices" ON devices;
DROP POLICY IF EXISTS "Users can manage their own devices" ON devices;
DROP POLICY IF EXISTS "Users can view their own photos" ON photos;
DROP POLICY IF EXISTS "Users can manage their own photos" ON photos;

-- Users policies
CREATE POLICY "Users can read all users"
ON users FOR SELECT
TO authenticated
USING (true);

-- Devices policies
CREATE POLICY "Users can view their own devices"
ON devices FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own devices"
ON devices FOR ALL
TO authenticated
USING (auth.uid() = user_id);

-- Photos policies
CREATE POLICY "Users can view their own photos"
ON photos FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own photos"
ON photos FOR ALL
TO authenticated
USING (auth.uid() = user_id);
