# إصلاح مشكلة انقطاع اتصال الهاتف
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   إصلاح مشكلة انقطاع اتصال الهاتف" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# مسار ADB
$adbPath = "C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe"

Write-Host "1. إيقاف ADB server..." -ForegroundColor Yellow
& $adbPath kill-server
Start-Sleep -Seconds 2

Write-Host "2. بدء ADB server..." -ForegroundColor Yellow
& $adbPath start-server
Start-Sleep -Seconds 2

Write-Host "3. فحص الأجهزة المتصلة..." -ForegroundColor Yellow
& $adbPath devices
Write-Host ""

Write-Host "4. فحص Flutter devices..." -ForegroundColor Yellow
flutter devices
Write-Host ""

Write-Host "========================================" -ForegroundColor Green
Write-Host "تم الانتهاء من إصلاح الاتصال" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# إضافة دالة لمراقبة الاتصال
function Watch-AdbConnection {
    param(
        [int]$IntervalSeconds = 10
    )
    
    Write-Host "بدء مراقبة اتصال الهاتف..." -ForegroundColor Cyan
    Write-Host "اضغط Ctrl+C للإيقاف" -ForegroundColor Yellow
    Write-Host ""
    
    while ($true) {
        $devices = & $adbPath devices
        $deviceCount = ($devices | Select-String "device$").Count
        
        $timestamp = Get-Date -Format "HH:mm:ss"
        if ($deviceCount -gt 0) {
            Write-Host "[$timestamp] ✅ الهاتف متصل ($deviceCount جهاز)" -ForegroundColor Green
        } else {
            Write-Host "[$timestamp] ❌ الهاتف غير متصل - محاولة إعادة الاتصال..." -ForegroundColor Red
            & $adbPath kill-server
            Start-Sleep -Seconds 1
            & $adbPath start-server
            Start-Sleep -Seconds 2
        }
        
        Start-Sleep -Seconds $IntervalSeconds
    }
}

Write-Host "لبدء مراقبة الاتصال، اكتب: Watch-AdbConnection" -ForegroundColor Cyan
