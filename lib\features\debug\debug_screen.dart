import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/config/supabase_config.dart';
import '../../core/services/users_service.dart';

class DebugScreen extends ConsumerStatefulWidget {
  const DebugScreen({super.key});

  @override
  ConsumerState<DebugScreen> createState() => _DebugScreenState();
}

class _DebugScreenState extends ConsumerState<DebugScreen> {
  String _debugInfo = 'بدء التشخيص...\n';
  bool _isRunning = false;

  @override
  void initState() {
    super.initState();
    _runDiagnostics();
  }

  void _addDebugInfo(String info) {
    setState(() {
      _debugInfo += '$info\n';
    });
  }

  Future<void> _runDiagnostics() async {
    setState(() {
      _isRunning = true;
      _debugInfo = 'بدء التشخيص...\n';
    });

    try {
      // 1. فحص متغيرات البيئة
      _addDebugInfo('1. فحص متغيرات البيئة:');
      try {
        final url = SupabaseConfig.supabaseUrl;
        final serviceKey = SupabaseConfig.supabaseServiceKey;
        final anonKey = SupabaseConfig.supabaseAnonKey;
        
        _addDebugInfo('   ✅ SUPABASE_URL: ${url.substring(0, 30)}...');
        _addDebugInfo('   ✅ SERVICE_KEY: ${serviceKey.substring(0, 30)}...');
        _addDebugInfo('   ✅ ANON_KEY: ${anonKey.substring(0, 30)}...');
      } catch (e) {
        _addDebugInfo('   ❌ خطأ في متغيرات البيئة: $e');
        return;
      }

      // 2. فحص اتصال Supabase
      _addDebugInfo('\n2. فحص اتصال Supabase:');
      try {
        final client = Supabase.instance.client;
        _addDebugInfo('   ✅ Supabase client متاح');
        
        // اختبار استعلام بسيط
        final response = await client.from('users').select('count').count();
        _addDebugInfo('   ✅ استعلام العد نجح: $response');
      } catch (e) {
        _addDebugInfo('   ❌ خطأ في اتصال Supabase: $e');
      }

      // 3. فحص جدول المستخدمين
      _addDebugInfo('\n3. فحص جدول المستخدمين:');
      try {
        final client = Supabase.instance.client;
        final response = await client
            .from('users')
            .select('id, full_name, national_id, account_status, created_at')
            .limit(5);
        
        _addDebugInfo('   ✅ استعلام المستخدمين نجح');
        _addDebugInfo('   📊 عدد المستخدمين المسترجعين: ${response.length}');
        
        if (response.isNotEmpty) {
          _addDebugInfo('   👤 أول مستخدم: ${response[0]}');
        } else {
          _addDebugInfo('   ⚠️ لا توجد مستخدمين في قاعدة البيانات');
        }
      } catch (e) {
        _addDebugInfo('   ❌ خطأ في استعلام المستخدمين: $e');
      }

      // 4. فحص خدمة المستخدمين
      _addDebugInfo('\n4. فحص خدمة المستخدمين:');
      try {
        final usersService = ref.read(usersServiceProvider);
        final users = await usersService.getAllUsers(forceRefresh: true);
        _addDebugInfo('   ✅ خدمة المستخدمين تعمل');
        _addDebugInfo('   📊 عدد المستخدمين من الخدمة: ${users.length}');
      } catch (e) {
        _addDebugInfo('   ❌ خطأ في خدمة المستخدمين: $e');
      }

      // 5. فحص صلاحيات RLS
      _addDebugInfo('\n5. فحص صلاحيات RLS:');
      try {
        final client = SupabaseClient(
          SupabaseConfig.supabaseUrl,
          SupabaseConfig.supabaseServiceKey,
          headers: {'apikey': SupabaseConfig.supabaseServiceKey},
        );
        
        final response = await client.from('users').select('*').limit(1);
        _addDebugInfo('   ✅ Service role key يعمل بشكل صحيح');
        _addDebugInfo('   📊 البيانات المسترجعة: ${response.length} سجل');
      } catch (e) {
        _addDebugInfo('   ❌ خطأ في صلاحيات RLS: $e');
      }

      _addDebugInfo('\n✅ انتهى التشخيص');
    } catch (e) {
      _addDebugInfo('\n❌ خطأ عام في التشخيص: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تشخيص النظام'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isRunning ? null : _runDiagnostics,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_isRunning)
              const LinearProgressIndicator(),
            const SizedBox(height: 16),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _debugInfo,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isRunning ? null : _runDiagnostics,
                    child: const Text('إعادة تشغيل التشخيص'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('العودة'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
