import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:moon_memory_admin/shared/widgets/search_bar_widget.dart';

void main() {
  group('SearchBarWidget', () {
    testWidgets('should display default hint text', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SearchBarWidget(),
          ),
        ),
      );

      expect(find.byType(TextField), findsOneWidget);
      
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.decoration?.hintText, equals('البحث...'));
    });

    testWidgets('should display custom hint text', (tester) async {
      const customHint = 'Search users...';
      
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SearchBarWidget(hintText: customHint),
          ),
        ),
      );

      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.decoration?.hintText, equals(customHint));
    });

    testWidgets('should display search icon', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SearchBarWidget(),
          ),
        ),
      );

      expect(find.byIcon(Icons.search), findsOneWidget);
    });

    testWidgets('should call onChanged when text changes', (tester) async {
      String? changedText;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SearchBarWidget(
              onChanged: (text) {
                changedText = text;
              },
            ),
          ),
        ),
      );

      const testText = 'test search';
      await tester.enterText(find.byType(TextField), testText);
      
      expect(changedText, equals(testText));
    });

    testWidgets('should show clear button when text is entered', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SearchBarWidget(),
          ),
        ),
      );

      // Initially no clear button
      expect(find.byIcon(Icons.clear), findsNothing);

      // Enter text
      await tester.enterText(find.byType(TextField), 'test');
      await tester.pump();

      // Clear button should appear
      expect(find.byIcon(Icons.clear), findsOneWidget);
    });

    testWidgets('should clear text when clear button is pressed', (tester) async {
      bool clearCalled = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SearchBarWidget(
              onClear: () {
                clearCalled = true;
              },
            ),
          ),
        ),
      );

      // Enter text
      await tester.enterText(find.byType(TextField), 'test');
      await tester.pump();

      // Tap clear button
      await tester.tap(find.byIcon(Icons.clear));
      await tester.pump();

      // Text should be cleared
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.controller?.text, isEmpty);
      expect(clearCalled, isTrue);
    });

    testWidgets('should use provided controller', (tester) async {
      final controller = TextEditingController(text: 'initial text');
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SearchBarWidget(controller: controller),
          ),
        ),
      );

      expect(find.text('initial text'), findsOneWidget);
      
      // Dispose controller
      controller.dispose();
    });

    testWidgets('should be disabled when enabled is false', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SearchBarWidget(enabled: false),
          ),
        ),
      );

      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.enabled, isFalse);
    });

    testWidgets('should be enabled by default', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SearchBarWidget(),
          ),
        ),
      );

      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.enabled, isTrue);
    });

    testWidgets('should hide clear button after clearing text', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SearchBarWidget(),
          ),
        ),
      );

      // Enter text
      await tester.enterText(find.byType(TextField), 'test');
      await tester.pump();

      // Clear button should be visible
      expect(find.byIcon(Icons.clear), findsOneWidget);

      // Clear text
      await tester.tap(find.byIcon(Icons.clear));
      await tester.pump();

      // Clear button should be hidden
      expect(find.byIcon(Icons.clear), findsNothing);
    });
  });
}
