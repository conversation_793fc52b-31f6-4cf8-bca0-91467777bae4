import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../core/services/users_service.dart';

class UserDetailsScreen extends ConsumerStatefulWidget {
  final String userId;
  final String userName;

  const UserDetailsScreen({
    super.key,
    required this.userId,
    required this.userName,
  });

  @override
  ConsumerState<UserDetailsScreen> createState() => _UserDetailsScreenState();
}

class _UserDetailsScreenState extends ConsumerState<UserDetailsScreen> {
  final _dateFormat = DateFormat('yyyy-MM-dd HH:mm');
  final _usersService = UsersService();

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: Text('تفاصيل المستخدم: ${widget.userName}'),
          bottom: const TabBar(
            tabs: [
              Tab(text: 'الأجهزة'),
              Tab(text: 'محاولات تسجيل الدخول'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            _buildDevicesTab(),
            _buildLoginAttemptsTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildDevicesTab() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _usersService.getUserDevices(widget.userId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('حدث خطأ: ${snapshot.error}'));
        }

        final devices = snapshot.data!;
        if (devices.isEmpty) {
          return const Center(child: Text('لا توجد أجهزة مسجلة'));
        }

        return ListView.builder(
          itemCount: devices.length,
          itemBuilder: (context, index) {
            final device = devices[index];
            final isActive = device['is_active'] as bool;
            final lastLogin = device['last_login'] != null
                ? _dateFormat.format(DateTime.parse(device['last_login']))
                : 'لم يسجل دخول بعد';

            return Card(
              margin: const EdgeInsets.all(8),
              child: ListTile(
                title: Text(device['device_name'] ?? 'جهاز غير معروف'),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('معرف الجهاز: ${device['device_id']}'),
                    Text('آخر تسجيل دخول: $lastLogin'),
                  ],
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Switch(
                      value: isActive,
                      onChanged: (value) async {
                        try {
                          await _usersService.toggleDeviceStatus(
                            device['id'],
                            value,
                          );
                          if (!context.mounted) return;
                          setState(() {});
                        } catch (e) {
                          if (!context.mounted) return;
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text(e.toString())),
                          );
                        }
                      },
                    ),
                    Icon(
                      isActive ? Icons.check_circle : Icons.cancel,
                      color: isActive ? Colors.green : Colors.red,
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildLoginAttemptsTab() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _usersService.getLoginAttempts(widget.userId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('حدث خطأ: ${snapshot.error}'));
        }

        final attempts = snapshot.data!;
        if (attempts.isEmpty) {
          return const Center(child: Text('لا توجد محاولات تسجيل دخول'));
        }

        return ListView.builder(
          itemCount: attempts.length,
          itemBuilder: (context, index) {
            final attempt = attempts[index];
            final success = attempt['success'] as bool;
            final timestamp = _dateFormat.format(
              DateTime.parse(attempt['created_at']),
            );

            return Card(
              margin: const EdgeInsets.all(8),
              child: ListTile(
                leading: Icon(
                  success ? Icons.check_circle : Icons.error,
                  color: success ? Colors.green : Colors.red,
                ),
                title: Text(attempt['device_name'] ?? 'جهاز غير معروف'),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('معرف الجهاز: ${attempt['device_id']}'),
                    Text('التاريخ: $timestamp'),
                    if (!success && attempt['failure_reason'] != null)
                      Text(
                        'سبب الفشل: ${attempt['failure_reason']}',
                        style: const TextStyle(color: Colors.red),
                      ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
